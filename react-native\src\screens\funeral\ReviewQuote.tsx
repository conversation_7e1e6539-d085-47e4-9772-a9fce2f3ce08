import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Alert,
  Switch,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { FuneralStackParamList } from '../../navigation/FuneralNavigator';

import Button from '../../components/common/Button';
import Card from '../../components/common/Card';

type NavigationProp = StackNavigationProp<FuneralStackParamList, 'ReviewQuote'>;
type RouteProp = RouteProp<FuneralStackParamList, 'ReviewQuote'>;

const FuneralReviewQuote: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RouteProp>();
  const { planDetails, beneficiaryDetails, debitDetails, emailDetails } = route.params;

  const [acceptTerms, setAcceptTerms] = useState(false);
  const [loading, setLoading] = useState(false);

  // Calculate premium based on plan
  const calculatePremium = () => {
    switch (planDetails.id) {
      case 'funeral_10k':
        return 25.00;
      case 'funeral_30k':
        return 75.00;
      case 'funeral_family':
        return 120.00;
      default:
        return 25.00;
    }
  };

  const premium = calculatePremium();
  const quoteReference = `FUN${Date.now().toString().slice(-6)}`;

  const handleSubmitApplication = async () => {
    if (!acceptTerms) {
      Alert.alert('Terms and Conditions', 'Please accept the terms and conditions to continue.');
      return;
    }

    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      // Show React Native POC indicator
      Alert.alert(
        'React Native POC - Application Submitted',
        `Quote Reference: ${quoteReference}\nPremium: R${premium}/month\n\nThis demonstrates a complete React Native insurance flow with state management across multiple screens.`,
        [
          {
            text: 'View Application',
            onPress: () => {
              navigation.navigate('Success', {
                quoteReference,
                premium,
              });
            },
          },
        ]
      );
      setLoading(false);
    }, 2000);
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>Review your quote</Text>
          <Text style={styles.subtitle}>
            Please review all details before submitting your application.
          </Text>
        </View>

        {/* Quote Summary */}
        <Card style={styles.quoteCard} variant="elevated" padding="large" margin="medium">
          <View style={styles.quoteHeader}>
            <Text style={styles.quoteTitle}>Quote Summary</Text>
            <Text style={styles.quoteReference}>Ref: {quoteReference}</Text>
          </View>
          
          <View style={styles.quoteLine}>
            <Text style={styles.quoteLabel}>Plan:</Text>
            <Text style={styles.quoteValue}>{planDetails.title}</Text>
          </View>
          
          <View style={styles.quoteLine}>
            <Text style={styles.quoteLabel}>Cover amount:</Text>
            <Text style={styles.quoteValue}>{planDetails.coverAmount}</Text>
          </View>
          
          <View style={[styles.quoteLine, styles.premiumLine]}>
            <Text style={styles.premiumLabel}>Monthly premium:</Text>
            <Text style={styles.premiumValue}>R{premium.toFixed(2)}</Text>
          </View>
        </Card>

        {/* Plan Details */}
        <Card style={styles.detailCard} variant="default" padding="large" margin="medium">
          <Text style={styles.sectionTitle}>Plan Details</Text>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Product:</Text>
            <Text style={styles.detailValue}>{planDetails.title}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Description:</Text>
            <Text style={styles.detailValue}>{planDetails.subtitle}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Cover amount:</Text>
            <Text style={styles.detailValue}>{planDetails.coverAmount}</Text>
          </View>
        </Card>

        {/* Beneficiary Details */}
        <Card style={styles.detailCard} variant="default" padding="large" margin="medium">
          <Text style={styles.sectionTitle}>Beneficiary Details</Text>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Name:</Text>
            <Text style={styles.detailValue}>
              {beneficiaryDetails.firstName} {beneficiaryDetails.lastName}
            </Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>ID Number:</Text>
            <Text style={styles.detailValue}>{beneficiaryDetails.idNumber}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Relationship:</Text>
            <Text style={styles.detailValue}>{beneficiaryDetails.relationship}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Contact:</Text>
            <Text style={styles.detailValue}>{beneficiaryDetails.contactNumber}</Text>
          </View>
        </Card>

        {/* Important Information */}
        <Card style={styles.infoCard} variant="outlined" padding="medium" margin="medium">
          <Text style={styles.infoTitle}>Important Information</Text>
          <Text style={styles.infoText}>
            • This quote is valid for 30 days{'\n'}
            • 6-month waiting period applies for natural death{'\n'}
            • No waiting period for accidental death{'\n'}
            • Premium may be adjusted based on final underwriting{'\n'}
            • Terms and conditions apply
          </Text>
        </Card>

        {/* Terms and Conditions */}
        <Card style={styles.termsCard} variant="flat" padding="medium" margin="medium">
          <View style={styles.termsRow}>
            <Switch
              value={acceptTerms}
              onValueChange={setAcceptTerms}
              trackColor={{ false: '#e0e0e0', true: '#00A651' }}
              thumbColor={acceptTerms ? '#ffffff' : '#f4f3f4'}
            />
            <Text style={styles.termsText}>
              I accept the{' '}
              <Text style={styles.termsLink}>terms and conditions</Text>
              {' '}and confirm that all information provided is accurate.
            </Text>
          </View>
        </Card>

        {/* React Native POC Notice */}
        <Card style={styles.pocNotice} variant="flat" padding="medium" margin="medium">
          <View style={styles.pocContent}>
            <Text style={styles.pocIcon}>🚀</Text>
            <Text style={styles.pocText}>
              React Native POC - Complete insurance flow with data persistence across screens
            </Text>
          </View>
        </Card>
      </ScrollView>

      <View style={styles.footer}>
        <Button
          title="Submit Application"
          onPress={handleSubmitApplication}
          disabled={!acceptTerms}
          loading={loading}
          fullWidth
          size="large"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    lineHeight: 22,
  },
  quoteCard: {
    backgroundColor: '#f8fff8',
    borderColor: '#00A651',
    borderWidth: 1,
  },
  quoteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  quoteTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  quoteReference: {
    fontSize: 14,
    color: '#666666',
  },
  quoteLine: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  quoteLabel: {
    fontSize: 14,
    color: '#666666',
  },
  quoteValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
  },
  premiumLine: {
    marginTop: 8,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  premiumLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
  },
  premiumValue: {
    fontSize: 18,
    fontWeight: '600',
    color: '#00A651',
  },
  detailCard: {
    marginHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666666',
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    color: '#333333',
    flex: 2,
    textAlign: 'right',
  },
  infoCard: {
    backgroundColor: '#fff9e6',
    borderColor: '#ffc107',
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  termsCard: {
    backgroundColor: '#f9f9f9',
  },
  termsRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  termsText: {
    fontSize: 14,
    color: '#333333',
    marginLeft: 12,
    flex: 1,
    lineHeight: 20,
  },
  termsLink: {
    color: '#00A651',
    textDecorationLine: 'underline',
  },
  pocNotice: {
    backgroundColor: '#FFF3E0',
    borderRadius: 8,
  },
  pocContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pocIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  pocText: {
    fontSize: 14,
    color: '#FF6B35',
    flex: 1,
    lineHeight: 20,
  },
  footer: {
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
});

export default FuneralReviewQuote;
