<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="za.co.nedbank">
    <!--
 Zoom SDK supports minimum app level 18 but currently app has minimum api level 17,
    below line is added to resolve build error due to api level mismatch
    -->
    <!-- <uses-sdk tools:overrideLibrary="com.facetec.zoom.sdk" /> -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false"
        tools:replace="required" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false"
        tools:replace="required" />
    <uses-feature
        android:name="android.hardware.camera.front"
        android:required="false"
        tools:replace="required" />
    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false"
        tools:replace="required" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />

    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <category android:name="android.intent.category.BROWSABLE" />

            <data android:scheme="http" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <category android:name="android.intent.category.BROWSABLE" />

            <data android:scheme="https" />
        </intent>

        <package android:name="za.co.nedbank.avoafrica.debug" />
        <package android:name="za.co.nedbank.avoafrica.stage" />
        <package android:name="za.co.nedbank.avoafrica.test" />
        <package android:name="za.co.nedbank.avoafrica" />
    </queries>

    <application
        android:name=".ui.NBApplication"
        android:allowBackup="false"
        android:icon="${appIcon}"
        android:label="@string/app_name"
        android:roundIcon="${appRoundIcon}"
        android:supportsRtl="true"
        android:theme="@style/AppTheme.NoActionBar"
        android:usesCleartextTraffic="true"
        tools:replace="android:allowBackup,android:name,android:icon,android:roundIcon">

        <activity
            android:name=".ui.view.home.verifyme.IDVLVerifyMeActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.refica.FicaErrorActivity"
            android:screenOrientation="portrait">

        </activity>
        <activity
            android:name=".ui.view.refica.FicaSuccessActivity"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.view.fatca.FatcaRestrictionMessageActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".services.familybanking.view.confirmation.SpouseAndChildConfirmationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".services.familybanking.view.spouseinvitation.FamilyBankingInviteAndAcceptConditionsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".services.familybanking.view.spouseinvitation.FamilyBankingInviteConfirmationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".services.familybanking.view.spouseinvitation.FamilyBankingDeclineInvitationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".services.familybanking.view.spouseinvitation.FamilyBankingInviteAndOpenAccountActivity"
            android:screenOrientation="portrait" />
        <activity android:name=".enroll_v2.view.ntf.view.ntf_login.NTFLoginActivity" />
        <activity
            android:name=".enroll_v2.view.ntf.view.ntf_webview.EnrollNTFWebviewActivity"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".enroll_v2.view.ntf.view.ntf_your_app.EnrollNTFYourAppActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar.FullScreen" />
        <activity
            android:name=".ui.view.developer_option.DeveloperOptionAlertActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.deeplink.view.AppDeeplinkActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.splashscreen.SplashScreenActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar.FullScreen">

            <!--
                 changing launcher activity in future needs corresponding checks for
                    Activity.isTaskRoot() inside activity onCreate()
            -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="nedbank.onelink.me"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter>
                <data android:scheme="nedbankretention" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <intent-filter>
                <data android:scheme="nedbanklanding" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <intent-filter
                android:autoVerify="true"
                tools:targetApi="m">
                <data
                    android:host="open"
                    android:scheme="nedbank" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <intent-filter>
                <action android:name="za.co.nedbank.ui.app_shortcut.SCAN_PAY_SHORTCUT" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <data android:scheme="nedbankngc" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.view.notification.transaction_notification.inbox.TransactionInboxActivity"
            android:label="@string/transaction_notification"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar"
            android:windowSoftInputMode="stateAlwaysHidden|adjustNothing" />
        <activity
            android:name=".ui.view.notification.transaction_notification.report_fraud.ReportFraudTransactionActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.notification.transaction_notification.details.TransactionNotificationDetailsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.notification.transaction_notification.sort_notifications.SortNotificationsActivity"
            android:label="@string/sort_transaction_notifications"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.notification.notification_preferences.NotificationPreferenceActivity"
            android:label="@string/notification_preferences_screen"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.notification.notification_permission.NotificationEnableActivity"
            android:label="@string/notification_enable_screen"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.notification.notification_preferences.account_preference.AccountPreferenceActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.notification.notification_preferences.all_accounts_preferences.AllAccountsPreferenceActivity"
            android:label="@string/account_notification_screen"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.notification.notification_preferences.delivery_preferences.DeliveryPreferencesActivity"
            android:label="@string/label_delivery_preference"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.notification.notification_details.redirect_url.RedirectUrlActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.notification.contextswitch.ContextSwitchConfirmationActivity"
            android:noHistory="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.home.HomeActivity"
            android:label="@string/label_home_screen"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar.WithoutFullScreen"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.view.refica.VerifyMeActivity"
            android:label="@string/label_verify_me_screen"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar.WithoutFullScreen"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.view.home.tax_certificates_accounts.TaxCertificatesAccountsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.home.add_recipient.AddRecipientActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.view.home.recipient_detail.RecipientDetailActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar" />
        <activity
            android:name=".ui.view.home.recipient_detail.RecipientDetailWithHistoryActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".ui.view.home.money_requests.view_money_response.ViewMoneyRequestsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar" />
        <activity
            android:name=".ui.view.home.money_requests.view_money_response.MoneyPaymentReviewActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar" />
        <activity
            android:name=".ui.view.home.edit_recipient.EditRecipientActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.view.ita.ita_authentication.ITAAuthenticationActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar" />

        <service
            android:name=".ui.notifications.NBFirebaseMessagingService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <service
            android:name=".ui.notifications.NBHmsMessagingService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <service
            android:name=".ui.notifications.NotificationNavigationService"
            android:exported="false" />
        <service
            android:name=".ui.ITANavigationService"
            android:exported="false" />

        <activity
            android:name=".ui.notifications.NotificationNavigationActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden" />

        <activity
            android:name=".ui.view.ita.ITAFlowActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".ui.view.ita.qrcodelogin.QrCodeProcessingActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".ui.view.ita.qrcodelogin.QrLoginResultActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden" />

        <meta-data
            android:name="firebase_crashlytics_collection_enabled"
            android:value="${enableCrashReporting}" />
        <meta-data
            android:name="net.hockeyapp.android.appIdentifier"
            android:value="${HOCKEYAPP_APP_ID}" />
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="@string/GCMKEY" />

        <activity
            android:name=".ui.view.coming_soon.ComingSoonActivity"
            android:label="@string/title_activity_coming_soon"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.pop.NewShareProofOfPaymentActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.pop.SharePopMethodsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.pop.TransactionDetailsActivity"
            android:screenOrientation="portrait" />

        <receiver
            android:name="com.appsflyer.MultipleInstallBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.android.vending.INSTALL_REFERRER" />
            </intent-filter>
        </receiver>

        <meta-data
            android:name="io.branch.sdk.BranchKey"
            android:value="@string/branch_key_live" />
        <meta-data
            android:name="io.branch.sdk.BranchKey.test"
            android:value="@string/branch_key_test" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/green" />

        <activity
            android:name=".ui.view.home.money_requests.send_money_request.RecipientContactsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar" />
        <activity
            android:name=".ui.view.home.money_requests.send_money_request.MoneyRequestActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".ui.view.home.money_requests.view_money_response.MoneyPaymentActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".ui.view.home.money_requests.view_money_response.MoneyPaymentSuccessActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".ui.view.home.money_requests.send_money_request.ViewMoneyRequestDetailsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".ui.view.home.money_requests.send_money_request.MoneyRequestSuccessActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".ui.view.notification.NotificationCenterActivity"
            android:label="@string/label_notification_centre"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            tools:node="merge">
            <meta-data
                android:name="net.danlew.android.joda.JodaTimeInitializer"
                tools:node="remove" />
        </provider>
        <provider
            android:name=".ui.MyProvider"
            android:authorities="${applicationId}"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>

        <activity
            android:name=".ui.view.notification.notification_details.NotificationDetailsActivity"
            android:configChanges="uiMode"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.notification.ajo_notification_details.AJONotificationDetailsActivity"
            android:configChanges="uiMode"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.notification.ajo_notification_details.navigation.NavigationHandlerActivity"
            android:configChanges="uiMode"
            android:theme="@style/AppTheme.Transparent" />
        <activity
            android:name=".ui.view.notification.notification_messages.NotificationMessagesActivity"
            android:label="@string/messages_inbox"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.home.cms_content.CMSMediaContentActivity"
            android:configChanges="uiMode"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.pop.success_pop.SuccessPopActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.pop.failure_pop.FailurePopActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.home.quick_pay.QuickPayActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.pay_me.PayMeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".services.moneymanager.view.flow_navigation.MoneyTrackerFlowNavigationActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar" />
        <activity
            android:name=".payment.crossborder.view.service_unavailable.RemittanceUnavailableActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.home.latest.latestWidget.LatestWidgetActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar" />
        <activity
            android:name=".ui.view.home.fc_deep_linking.DeepLinkIntermediateActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.retention.RetentionTaskSelectionActivity"
            android:screenOrientation="portrait"
            android:taskAffinity="za.co.nedbank.services.view.account.account_details_activity"
            android:theme="@style/AppTheme.Contrast.NoActionBar" />
        <activity
            android:name=".ui.view.retention.RetentionWelcomeActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar" />
        <activity
            android:name=".ui.view.retention.feedback.RetentionFeedbackActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar" />
        <activity
            android:name=".ui.view.retention.MultipleAccountsShareActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar" />
        <activity
            android:name=".ui.view.retention.feedback.RetentionFeedbackSuccessActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar" />
        <activity
            android:name=".ui.view.retention.notification_journey.RetentionInformationActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar" />
        <activity
            android:name=".ui.view.retention.notification_journey.RetentionNotificationJourneyActivity"
            android:screenOrientation="portrait"
            android:taskAffinity="za.co.nedbank.services.view.account.account_details_activity"
            android:theme="@style/AppTheme.Contrast.NoActionBar" />
        <activity
            android:name=".ui.view.home.investments.AccountTypeInvestmentActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Contrast.NoActionBar" />
        <activity
            android:name=".ui.view.card_delivery.branch_confirmation.CardDeliveryBranchConfirmationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.card_delivery.locker_confirmation.CardDeliveryLockerConfirmationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.card_delivery.deliver_to_me_confirmation.DeliverToMeConfirmationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="za.co.nedbank.ui.view.card_delivery.deliver_result.CardDeliveryResultActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="za.co.nedbank.ui.view.card_delivery.delivery_options.CardDeliveryOptionsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="za.co.nedbank.ui.view.card_delivery.locker_map.LockerMapActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.home.product_deep_linking.ProductDeepLinkActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.view.error.GenericTechnicalErrorActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.view.home.odd_restriction.OddRestrictionErrorActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.view.home.new_user.SalesUserApplicationDashboard"
            android:screenOrientation="portrait" />

        <!-- React Native Insurance Dashboard POC -->
        <activity
            android:name=".ui.view.reactnative.ReactNativeInsuranceActivity"
            android:label="Insurance Dashboard (React Native)"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
    </application>

</manifest>