import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Linking,
  Alert,
} from 'react-native';

import Card from '../../components/common/Card';

interface EmergencyContact {
  id: string;
  title: string;
  description: string;
  phoneNumber: string;
  category: 'medical' | 'security' | 'roadside' | 'general';
  available24h: boolean;
  icon: string;
}

const EmergencyServices: React.FC = () => {
  const emergencyContacts: EmergencyContact[] = [
    {
      id: '1',
      title: 'Emergency Medical Services',
      description: 'Ambulance and medical emergency response',
      phoneNumber: '10177',
      category: 'medical',
      available24h: true,
      icon: '🚑',
    },
    {
      id: '2',
      title: 'Police Emergency',
      description: 'Police emergency response and crime reporting',
      phoneNumber: '10111',
      category: 'security',
      available24h: true,
      icon: '🚔',
    },
    {
      id: '3',
      title: 'Fire Department',
      description: 'Fire emergency and rescue services',
      phoneNumber: '10177',
      category: 'security',
      available24h: true,
      icon: '🚒',
    },
    {
      id: '4',
      title: 'AA Roadside Assistance',
      description: 'Vehicle breakdown and roadside assistance',
      phoneNumber: '0861 000 234',
      category: 'roadside',
      available24h: true,
      icon: '🔧',
    },
    {
      id: '5',
      title: 'Nedbank Insurance Claims',
      description: 'Report insurance claims 24/7',
      phoneNumber: '0860 555 111',
      category: 'general',
      available24h: true,
      icon: '📋',
    },
    {
      id: '6',
      title: 'Poison Information Centre',
      description: 'Poison emergency information and advice',
      phoneNumber: '0861 555 777',
      category: 'medical',
      available24h: true,
      icon: '☠️',
    },
    {
      id: '7',
      title: 'Disaster Management',
      description: 'Natural disaster emergency response',
      phoneNumber: '0800 200 200',
      category: 'general',
      available24h: true,
      icon: '🌪️',
    },
    {
      id: '8',
      title: 'Gender-Based Violence',
      description: 'Support and assistance for GBV victims',
      phoneNumber: '0800 428 428',
      category: 'general',
      available24h: true,
      icon: '🆘',
    },
  ];

  const handleCallPress = (contact: EmergencyContact) => {
    Alert.alert(
      'Emergency Call',
      `Call ${contact.title}?\n\nNumber: ${contact.phoneNumber}\n\nNote: This is a React Native POC. In production, this would initiate an actual phone call.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Call',
          onPress: () => {
            // In production, this would make an actual call
            // Linking.openURL(`tel:${contact.phoneNumber}`);
            Alert.alert(
              'React Native POC',
              `This would call ${contact.phoneNumber} for ${contact.title}. React Native can handle phone calls, SMS, and other device integrations seamlessly.`,
              [{ text: 'OK' }]
            );
          },
        },
      ]
    );
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'medical':
        return '#dc3545';
      case 'security':
        return '#007bff';
      case 'roadside':
        return '#ffc107';
      case 'general':
        return '#00A651';
      default:
        return '#666666';
    }
  };

  const renderEmergencyCard = (contact: EmergencyContact) => (
    <Card
      key={contact.id}
      style={[styles.emergencyCard, { borderLeftColor: getCategoryColor(contact.category) }]}
      onPress={() => handleCallPress(contact)}
      variant="default"
      padding="medium"
      margin="small"
    >
      <View style={styles.cardHeader}>
        <View style={styles.iconContainer}>
          <Text style={styles.icon}>{contact.icon}</Text>
        </View>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{contact.title}</Text>
          {contact.available24h && (
            <View style={styles.availabilityBadge}>
              <Text style={styles.availabilityText}>24/7</Text>
            </View>
          )}
        </View>
      </View>
      
      <Text style={styles.description}>{contact.description}</Text>
      
      <View style={styles.phoneContainer}>
        <Text style={styles.phoneLabel}>Emergency number:</Text>
        <Text style={styles.phoneNumber}>{contact.phoneNumber}</Text>
      </View>
      
      <View style={styles.callButton}>
        <Text style={styles.callButtonText}>Tap to call</Text>
      </View>
    </Card>
  );

  const groupedContacts = emergencyContacts.reduce((groups, contact) => {
    const category = contact.category;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(contact);
    return groups;
  }, {} as Record<string, EmergencyContact[]>);

  const categoryTitles = {
    medical: 'Medical Emergency',
    security: 'Security & Safety',
    roadside: 'Roadside Assistance',
    general: 'General Emergency',
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Emergency Services</Text>
          <Text style={styles.headerSubtitle}>
            Important emergency contact numbers for immediate assistance.
          </Text>
        </View>

        {/* Important Notice */}
        <Card style={styles.noticeCard} variant="outlined" padding="medium" margin="medium">
          <View style={styles.noticeHeader}>
            <Text style={styles.noticeIcon}>⚠️</Text>
            <Text style={styles.noticeTitle}>Important Notice</Text>
          </View>
          <Text style={styles.noticeText}>
            In a life-threatening emergency, call 10111 (Police) or 10177 (Medical) immediately.
            These numbers are free from any phone.
          </Text>
        </Card>

        {/* Emergency Contacts by Category */}
        {Object.entries(groupedContacts).map(([category, contacts]) => (
          <View key={category} style={styles.categorySection}>
            <Text style={styles.categoryTitle}>
              {categoryTitles[category as keyof typeof categoryTitles]}
            </Text>
            {contacts.map(renderEmergencyCard)}
          </View>
        ))}

        {/* Additional Information */}
        <Card style={styles.infoCard} variant="default" padding="large" margin="medium">
          <Text style={styles.infoTitle}>Additional Information</Text>
          <Text style={styles.infoText}>
            • Keep this list easily accessible{'\n'}
            • Save important numbers in your phone contacts{'\n'}
            • Know your location when calling emergency services{'\n'}
            • Stay calm and speak clearly{'\n'}
            • Follow the operator's instructions
          </Text>
        </Card>

        {/* React Native POC Notice */}
        <Card style={styles.pocNotice} variant="flat" padding="medium" margin="medium">
          <View style={styles.pocContent}>
            <Text style={styles.pocIcon}>📱</Text>
            <Text style={styles.pocText}>
              React Native POC - Emergency services with device integration capabilities (phone calls, SMS, location services)
            </Text>
          </View>
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#666666',
    lineHeight: 22,
  },
  noticeCard: {
    backgroundColor: '#fff3cd',
    borderColor: '#ffc107',
  },
  noticeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  noticeIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  noticeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  noticeText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  categorySection: {
    marginBottom: 20,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginLeft: 20,
    marginBottom: 10,
  },
  emergencyCard: {
    borderLeftWidth: 4,
    marginHorizontal: 10,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f8f8f8',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  icon: {
    fontSize: 20,
  },
  titleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
  },
  availabilityBadge: {
    backgroundColor: '#00A651',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  availabilityText: {
    fontSize: 10,
    color: '#ffffff',
    fontWeight: '500',
  },
  description: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 12,
    lineHeight: 20,
  },
  phoneContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  phoneLabel: {
    fontSize: 14,
    color: '#666666',
  },
  phoneNumber: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007bff',
  },
  callButton: {
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  callButtonText: {
    fontSize: 14,
    color: '#007bff',
    fontWeight: '500',
  },
  infoCard: {
    backgroundColor: '#f8f9fa',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 22,
  },
  pocNotice: {
    backgroundColor: '#FFF3E0',
    borderRadius: 8,
  },
  pocContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pocIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  pocText: {
    fontSize: 14,
    color: '#FF6B35',
    flex: 1,
    lineHeight: 20,
  },
});

export default EmergencyServices;
