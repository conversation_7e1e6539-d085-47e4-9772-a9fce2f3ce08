import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TextInput,
  Alert,
  Switch,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { FuneralStackParamList } from '../../navigation/FuneralNavigator';

import Button from '../../components/common/Button';
import Card from '../../components/common/Card';

type NavigationProp = StackNavigationProp<FuneralStackParamList, 'DebitOrder'>;
type RouteProp = RouteProp<FuneralStackParamList, 'DebitOrder'>;

interface DebitOrderForm {
  accountNumber: string;
  accountType: string;
  bankName: string;
  branchCode: string;
  accountHolder: string;
  debitDay: string;
  authorizeDebit: boolean;
}

const FuneralDebitOrder: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RouteProp>();
  const { planDetails, beneficiaryDetails } = route.params;

  const [form, setForm] = useState<DebitOrderForm>({
    accountNumber: '',
    accountType: 'Cheque',
    bankName: 'Nedbank',
    branchCode: '',
    accountHolder: '',
    debitDay: '1',
    authorizeDebit: false,
  });
  const [loading, setLoading] = useState(false);

  const updateForm = (field: keyof DebitOrderForm, value: string | boolean) => {
    setForm(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = (): boolean => {
    if (!form.accountNumber.trim() || form.accountNumber.length < 8) {
      Alert.alert('Validation Error', 'Please enter a valid account number.');
      return false;
    }
    if (!form.branchCode.trim() || form.branchCode.length !== 6) {
      Alert.alert('Validation Error', 'Please enter a valid 6-digit branch code.');
      return false;
    }
    if (!form.accountHolder.trim()) {
      Alert.alert('Validation Error', 'Please enter the account holder name.');
      return false;
    }
    if (!form.authorizeDebit) {
      Alert.alert('Authorization Required', 'Please authorize the debit order to continue.');
      return false;
    }
    return true;
  };

  const handleContinue = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      Alert.alert(
        'React Native POC',
        `Debit Order Setup Complete\nAccount: ${form.accountNumber}\nBank: ${form.bankName}\nDebit Day: ${form.debitDay}\n\nThis demonstrates React Native's capability for secure financial form handling.`,
        [
          {
            text: 'Continue',
            onPress: () => {
              navigation.navigate('Email', {
                planDetails,
                beneficiaryDetails,
                debitDetails: form,
              });
            },
          },
        ]
      );
      setLoading(false);
    }, 1000);
  };

  const accountTypes = ['Cheque', 'Savings', 'Transmission'];
  const debitDays = ['1', '15', '25'];

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Plan Summary */}
        <Card style={styles.planSummary} variant="outlined" padding="medium" margin="medium">
          <Text style={styles.planSummaryTitle}>Selected Plan</Text>
          <Text style={styles.planSummaryText}>{planDetails.title}</Text>
          <Text style={styles.planSummarySubtext}>{planDetails.premium}</Text>
        </Card>

        <View style={styles.header}>
          <Text style={styles.title}>Payment details</Text>
          <Text style={styles.subtitle}>
            Set up your debit order for monthly premium payments.
          </Text>
        </View>

        <Card style={styles.formCard} variant="default" padding="large" margin="medium">
          {/* Bank Selection */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Bank *</Text>
            <View style={styles.bankSelection}>
              <Text style={styles.selectedBank}>Nedbank</Text>
              <Text style={styles.bankNote}>Your Nedbank account will be used</Text>
            </View>
          </View>

          {/* Account Number */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Account number *</Text>
            <TextInput
              style={styles.input}
              value={form.accountNumber}
              onChangeText={(value) => updateForm('accountNumber', value)}
              placeholder="Enter account number"
              placeholderTextColor="#999999"
              keyboardType="numeric"
              secureTextEntry
            />
          </View>

          {/* Account Type */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Account type *</Text>
            <View style={styles.pickerContainer}>
              {accountTypes.map((type) => (
                <Button
                  key={type}
                  title={type}
                  onPress={() => updateForm('accountType', type)}
                  variant={form.accountType === type ? 'primary' : 'outline'}
                  size="small"
                  style={styles.pickerButton}
                />
              ))}
            </View>
          </View>

          {/* Branch Code */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Branch code *</Text>
            <TextInput
              style={styles.input}
              value={form.branchCode}
              onChangeText={(value) => updateForm('branchCode', value)}
              placeholder="Enter 6-digit branch code"
              placeholderTextColor="#999999"
              keyboardType="numeric"
              maxLength={6}
            />
          </View>

          {/* Account Holder */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Account holder name *</Text>
            <TextInput
              style={styles.input}
              value={form.accountHolder}
              onChangeText={(value) => updateForm('accountHolder', value)}
              placeholder="Enter account holder name"
              placeholderTextColor="#999999"
            />
          </View>

          {/* Debit Day */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Debit day *</Text>
            <View style={styles.pickerContainer}>
              {debitDays.map((day) => (
                <Button
                  key={day}
                  title={`${day}${day === '1' ? 'st' : day === '15' ? 'th' : 'th'}`}
                  onPress={() => updateForm('debitDay', day)}
                  variant={form.debitDay === day ? 'primary' : 'outline'}
                  size="small"
                  style={styles.pickerButton}
                />
              ))}
            </View>
          </View>

          {/* Authorization */}
          <View style={styles.authorizationSection}>
            <View style={styles.authorizationRow}>
              <Switch
                value={form.authorizeDebit}
                onValueChange={(value) => updateForm('authorizeDebit', value)}
                trackColor={{ false: '#e0e0e0', true: '#00A651' }}
                thumbColor={form.authorizeDebit ? '#ffffff' : '#f4f3f4'}
              />
              <Text style={styles.authorizationText}>
                I authorize Nedbank to debit my account monthly for the premium amount
              </Text>
            </View>
          </View>
        </Card>

        {/* Security Notice */}
        <Card style={styles.securityNotice} variant="outlined" padding="medium" margin="medium">
          <View style={styles.securityHeader}>
            <Text style={styles.securityIcon}>🔒</Text>
            <Text style={styles.securityTitle}>Secure Payment</Text>
          </View>
          <Text style={styles.securityText}>
            Your banking details are encrypted and secure. We use industry-standard security measures to protect your information.
          </Text>
        </Card>

        {/* React Native POC Notice */}
        <Card style={styles.pocNotice} variant="flat" padding="medium" margin="medium">
          <View style={styles.pocContent}>
            <Text style={styles.pocIcon}>💳</Text>
            <Text style={styles.pocText}>
              React Native POC - Secure financial form handling with validation and encryption capabilities
            </Text>
          </View>
        </Card>
      </ScrollView>

      <View style={styles.footer}>
        <Button
          title="Continue"
          onPress={handleContinue}
          loading={loading}
          fullWidth
          size="large"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  scrollView: {
    flex: 1,
  },
  planSummary: {
    backgroundColor: '#f8fff8',
    borderColor: '#00A651',
  },
  planSummaryTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
    marginBottom: 4,
  },
  planSummaryText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 2,
  },
  planSummarySubtext: {
    fontSize: 14,
    color: '#00A651',
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    lineHeight: 22,
  },
  formCard: {
    marginHorizontal: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333333',
    backgroundColor: '#ffffff',
  },
  bankSelection: {
    padding: 16,
    backgroundColor: '#f8fff8',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#00A651',
  },
  selectedBank: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  bankNote: {
    fontSize: 14,
    color: '#666666',
  },
  pickerContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  pickerButton: {
    marginRight: 8,
    marginBottom: 8,
  },
  authorizationSection: {
    marginTop: 20,
    padding: 16,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
  },
  authorizationRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  authorizationText: {
    fontSize: 14,
    color: '#333333',
    marginLeft: 12,
    flex: 1,
    lineHeight: 20,
  },
  securityNotice: {
    backgroundColor: '#f0f8ff',
    borderColor: '#007bff',
  },
  securityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  securityIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  securityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  securityText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  pocNotice: {
    backgroundColor: '#FFF3E0',
    borderRadius: 8,
  },
  pocContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pocIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  pocText: {
    fontSize: 14,
    color: '#FF6B35',
    flex: 1,
    lineHeight: 20,
  },
  footer: {
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
});

export default FuneralDebitOrder;
