import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { FuneralStackParamList } from '../../navigation/FuneralNavigator';

import Button from '../../components/common/Button';
import Card from '../../components/common/Card';

type NavigationProp = StackNavigationProp<FuneralStackParamList, 'PlanSetup'>;

interface PlanOption {
  id: string;
  title: string;
  subtitle: string;
  coverAmount: string;
  premium: string;
  features: string[];
  popular?: boolean;
}

const FuneralPlanSetup: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const planOptions: PlanOption[] = [
    {
      id: 'funeral_10k',
      title: 'Funeral 10K',
      subtitle: 'Essential funeral cover',
      coverAmount: 'Up to R10,000',
      premium: 'From R25/month',
      features: [
        'Funeral expenses covered',
        'Immediate family covered',
        'No waiting period for accidents',
        '6-month waiting period for natural death',
      ],
    },
    {
      id: 'funeral_30k',
      title: 'Funeral 30K',
      subtitle: 'Comprehensive funeral cover',
      coverAmount: 'Up to R30,000',
      premium: 'From R75/month',
      features: [
        'Funeral expenses covered',
        'Extended family covered',
        'No waiting period for accidents',
        '6-month waiting period for natural death',
        'Additional benefits included',
      ],
      popular: true,
    },
    {
      id: 'funeral_family',
      title: 'Family Funeral Plan',
      subtitle: 'Complete family protection',
      coverAmount: 'Up to R50,000',
      premium: 'From R120/month',
      features: [
        'Whole family covered',
        'Comprehensive funeral expenses',
        'No waiting period for accidents',
        '6-month waiting period for natural death',
        'Premium benefits included',
        'Grief counselling support',
      ],
    },
  ];

  const handlePlanSelect = (planId: string) => {
    setSelectedPlan(planId);
  };

  const handleContinue = async () => {
    if (!selectedPlan) {
      Alert.alert('Please select a plan', 'Choose a funeral plan to continue with your quote.');
      return;
    }

    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      const selectedPlanData = planOptions.find(plan => plan.id === selectedPlan);
      
      // Show React Native POC indicator
      Alert.alert(
        'React Native POC',
        `Selected: ${selectedPlanData?.title}\n\nThis demonstrates React Native navigation and state management in the insurance module.`,
        [
          {
            text: 'Continue',
            onPress: () => {
              navigation.navigate('Beneficiary', {
                planDetails: selectedPlanData,
              });
            },
          },
        ]
      );
      setLoading(false);
    }, 1000);
  };

  const renderPlanCard = (plan: PlanOption) => (
    <Card
      key={plan.id}
      style={[
        styles.planCard,
        selectedPlan === plan.id && styles.selectedPlanCard,
      ]}
      onPress={() => handlePlanSelect(plan.id)}
      variant="outlined"
      padding="medium"
      margin="small"
    >
      <View style={styles.planHeader}>
        <View style={styles.planTitleContainer}>
          <Text style={styles.planTitle}>{plan.title}</Text>
          {plan.popular && (
            <View style={styles.popularBadge}>
              <Text style={styles.popularText}>Most Popular</Text>
            </View>
          )}
        </View>
        <Text style={styles.planSubtitle}>{plan.subtitle}</Text>
      </View>

      <View style={styles.planDetails}>
        <View style={styles.coverRow}>
          <Text style={styles.coverLabel}>Cover amount:</Text>
          <Text style={styles.coverAmount}>{plan.coverAmount}</Text>
        </View>
        <View style={styles.premiumRow}>
          <Text style={styles.premiumLabel}>Premium:</Text>
          <Text style={styles.premiumAmount}>{plan.premium}</Text>
        </View>
      </View>

      <View style={styles.featuresContainer}>
        <Text style={styles.featuresTitle}>What's included:</Text>
        {plan.features.map((feature, index) => (
          <View key={index} style={styles.featureRow}>
            <Text style={styles.featureBullet}>•</Text>
            <Text style={styles.featureText}>{feature}</Text>
          </View>
        ))}
      </View>

      {selectedPlan === plan.id && (
        <View style={styles.selectedIndicator}>
          <Text style={styles.selectedText}>✓ Selected</Text>
        </View>
      )}
    </Card>
  );

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>Choose your funeral plan</Text>
          <Text style={styles.subtitle}>
            Select the plan that best suits your needs and budget.
          </Text>
        </View>

        <View style={styles.plansContainer}>
          {planOptions.map(renderPlanCard)}
        </View>

        {/* React Native POC Notice */}
        <Card style={styles.pocNotice} variant="flat" padding="medium" margin="medium">
          <View style={styles.pocContent}>
            <Text style={styles.pocIcon}>ℹ️</Text>
            <Text style={styles.pocText}>
              React Native POC - This demonstrates identical funeral insurance flow with cross-platform code sharing
            </Text>
          </View>
        </Card>
      </ScrollView>

      <View style={styles.footer}>
        <Button
          title="Continue"
          onPress={handleContinue}
          disabled={!selectedPlan}
          loading={loading}
          fullWidth
          size="large"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    lineHeight: 22,
  },
  plansContainer: {
    paddingHorizontal: 10,
  },
  planCard: {
    borderWidth: 2,
    borderColor: '#e0e0e0',
  },
  selectedPlanCard: {
    borderColor: '#00A651',
    backgroundColor: '#f8fff8',
  },
  planHeader: {
    marginBottom: 16,
  },
  planTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  planTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
  },
  popularBadge: {
    backgroundColor: '#00A651',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularText: {
    fontSize: 12,
    color: '#ffffff',
    fontWeight: '500',
  },
  planSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
  planDetails: {
    marginBottom: 16,
  },
  coverRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  coverLabel: {
    fontSize: 14,
    color: '#666666',
  },
  coverAmount: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
  },
  premiumRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  premiumLabel: {
    fontSize: 14,
    color: '#666666',
  },
  premiumAmount: {
    fontSize: 14,
    fontWeight: '600',
    color: '#00A651',
  },
  featuresContainer: {
    marginBottom: 12,
  },
  featuresTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 8,
  },
  featureRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  featureBullet: {
    fontSize: 14,
    color: '#00A651',
    marginRight: 8,
    marginTop: 2,
  },
  featureText: {
    fontSize: 14,
    color: '#666666',
    flex: 1,
    lineHeight: 20,
  },
  selectedIndicator: {
    alignItems: 'center',
    marginTop: 8,
  },
  selectedText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#00A651',
  },
  pocNotice: {
    backgroundColor: '#FFF3E0',
    borderRadius: 8,
  },
  pocContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pocIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  pocText: {
    fontSize: 14,
    color: '#FF6B35',
    flex: 1,
    lineHeight: 20,
  },
  footer: {
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
});

export default FuneralPlanSetup;
