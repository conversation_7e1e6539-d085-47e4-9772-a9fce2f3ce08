import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TextInput,
  Alert,
  Switch,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { FuneralStackParamList } from '../../navigation/FuneralNavigator';

import Button from '../../components/common/Button';
import Card from '../../components/common/Card';

type NavigationProp = StackNavigationProp<FuneralStackParamList, 'Email'>;
type RouteProp = RouteProp<FuneralStackParamList, 'Email'>;

interface EmailForm {
  emailAddress: string;
  confirmEmail: string;
  cellphoneNumber: string;
  receiveMarketing: boolean;
  receiveSMS: boolean;
  receiveEmail: boolean;
}

const FuneralEmail: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RouteProp>();
  const { planDetails, beneficiaryDetails, debitDetails } = route.params;

  const [form, setForm] = useState<EmailForm>({
    emailAddress: '',
    confirmEmail: '',
    cellphoneNumber: '',
    receiveMarketing: true,
    receiveSMS: true,
    receiveEmail: true,
  });
  const [loading, setLoading] = useState(false);

  const updateForm = (field: keyof EmailForm, value: string | boolean) => {
    setForm(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = (): boolean => {
    if (!form.emailAddress.trim() || !form.emailAddress.includes('@')) {
      Alert.alert('Validation Error', 'Please enter a valid email address.');
      return false;
    }
    if (form.emailAddress !== form.confirmEmail) {
      Alert.alert('Validation Error', 'Email addresses do not match.');
      return false;
    }
    if (!form.cellphoneNumber.trim() || form.cellphoneNumber.length < 10) {
      Alert.alert('Validation Error', 'Please enter a valid cellphone number.');
      return false;
    }
    return true;
  };

  const handleContinue = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      Alert.alert(
        'React Native POC',
        `Contact Details Complete\nEmail: ${form.emailAddress}\nCellphone: ${form.cellphoneNumber}\n\nThis demonstrates React Native's communication preferences and validation handling.`,
        [
          {
            text: 'Continue to Review',
            onPress: () => {
              navigation.navigate('ReviewQuote', {
                planDetails,
                beneficiaryDetails,
                debitDetails,
                emailDetails: form,
              });
            },
          },
        ]
      );
      setLoading(false);
    }, 1000);
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Plan Summary */}
        <Card style={styles.planSummary} variant="outlined" padding="medium" margin="medium">
          <Text style={styles.planSummaryTitle}>Selected Plan</Text>
          <Text style={styles.planSummaryText}>{planDetails.title}</Text>
          <Text style={styles.planSummarySubtext}>{planDetails.premium}</Text>
        </Card>

        <View style={styles.header}>
          <Text style={styles.title}>Contact details</Text>
          <Text style={styles.subtitle}>
            Provide your contact details for policy documents and communications.
          </Text>
        </View>

        <Card style={styles.formCard} variant="default" padding="large" margin="medium">
          {/* Email Address */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Email address *</Text>
            <TextInput
              style={styles.input}
              value={form.emailAddress}
              onChangeText={(value) => updateForm('emailAddress', value)}
              placeholder="Enter email address"
              placeholderTextColor="#999999"
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          {/* Confirm Email */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Confirm email address *</Text>
            <TextInput
              style={[
                styles.input,
                form.confirmEmail && form.emailAddress !== form.confirmEmail && styles.inputError
              ]}
              value={form.confirmEmail}
              onChangeText={(value) => updateForm('confirmEmail', value)}
              placeholder="Confirm email address"
              placeholderTextColor="#999999"
              keyboardType="email-address"
              autoCapitalize="none"
            />
            {form.confirmEmail && form.emailAddress !== form.confirmEmail && (
              <Text style={styles.errorText}>Email addresses do not match</Text>
            )}
          </View>

          {/* Cellphone Number */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Cellphone number *</Text>
            <TextInput
              style={styles.input}
              value={form.cellphoneNumber}
              onChangeText={(value) => updateForm('cellphoneNumber', value)}
              placeholder="Enter cellphone number"
              placeholderTextColor="#999999"
              keyboardType="phone-pad"
            />
          </View>
        </Card>

        {/* Communication Preferences */}
        <Card style={styles.preferencesCard} variant="default" padding="large" margin="medium">
          <Text style={styles.sectionTitle}>Communication preferences</Text>
          <Text style={styles.sectionSubtitle}>
            Choose how you'd like to receive updates about your policy.
          </Text>

          <View style={styles.preferenceItem}>
            <View style={styles.preferenceContent}>
              <Text style={styles.preferenceTitle}>Email notifications</Text>
              <Text style={styles.preferenceDescription}>
                Receive policy documents and important updates via email
              </Text>
            </View>
            <Switch
              value={form.receiveEmail}
              onValueChange={(value) => updateForm('receiveEmail', value)}
              trackColor={{ false: '#e0e0e0', true: '#00A651' }}
              thumbColor={form.receiveEmail ? '#ffffff' : '#f4f3f4'}
            />
          </View>

          <View style={styles.preferenceItem}>
            <View style={styles.preferenceContent}>
              <Text style={styles.preferenceTitle}>SMS notifications</Text>
              <Text style={styles.preferenceDescription}>
                Receive important alerts and reminders via SMS
              </Text>
            </View>
            <Switch
              value={form.receiveSMS}
              onValueChange={(value) => updateForm('receiveSMS', value)}
              trackColor={{ false: '#e0e0e0', true: '#00A651' }}
              thumbColor={form.receiveSMS ? '#ffffff' : '#f4f3f4'}
            />
          </View>

          <View style={styles.preferenceItem}>
            <View style={styles.preferenceContent}>
              <Text style={styles.preferenceTitle}>Marketing communications</Text>
              <Text style={styles.preferenceDescription}>
                Receive information about new products and special offers
              </Text>
            </View>
            <Switch
              value={form.receiveMarketing}
              onValueChange={(value) => updateForm('receiveMarketing', value)}
              trackColor={{ false: '#e0e0e0', true: '#00A651' }}
              thumbColor={form.receiveMarketing ? '#ffffff' : '#f4f3f4'}
            />
          </View>
        </Card>

        {/* Privacy Notice */}
        <Card style={styles.privacyNotice} variant="outlined" padding="medium" margin="medium">
          <View style={styles.privacyHeader}>
            <Text style={styles.privacyIcon}>🔒</Text>
            <Text style={styles.privacyTitle}>Privacy & Security</Text>
          </View>
          <Text style={styles.privacyText}>
            Your personal information is protected in accordance with POPIA and our privacy policy. You can update your communication preferences at any time.
          </Text>
        </Card>

        {/* React Native POC Notice */}
        <Card style={styles.pocNotice} variant="flat" padding="medium" margin="medium">
          <View style={styles.pocContent}>
            <Text style={styles.pocIcon}>📧</Text>
            <Text style={styles.pocText}>
              React Native POC - Communication preferences with real-time validation and privacy compliance
            </Text>
          </View>
        </Card>
      </ScrollView>

      <View style={styles.footer}>
        <Button
          title="Continue to Review"
          onPress={handleContinue}
          loading={loading}
          fullWidth
          size="large"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  scrollView: {
    flex: 1,
  },
  planSummary: {
    backgroundColor: '#f8fff8',
    borderColor: '#00A651',
  },
  planSummaryTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
    marginBottom: 4,
  },
  planSummaryText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 2,
  },
  planSummarySubtext: {
    fontSize: 14,
    color: '#00A651',
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    lineHeight: 22,
  },
  formCard: {
    marginHorizontal: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333333',
    backgroundColor: '#ffffff',
  },
  inputError: {
    borderColor: '#dc3545',
  },
  errorText: {
    fontSize: 12,
    color: '#dc3545',
    marginTop: 4,
  },
  preferencesCard: {
    marginHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 20,
    lineHeight: 20,
  },
  preferenceItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  preferenceContent: {
    flex: 1,
    marginRight: 16,
  },
  preferenceTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 4,
  },
  preferenceDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  privacyNotice: {
    backgroundColor: '#f0f8ff',
    borderColor: '#007bff',
  },
  privacyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  privacyIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  privacyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  privacyText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  pocNotice: {
    backgroundColor: '#FFF3E0',
    borderRadius: 8,
  },
  pocContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pocIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  pocText: {
    fontSize: 14,
    color: '#FF6B35',
    flex: 1,
    lineHeight: 20,
  },
  footer: {
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
});

export default FuneralEmail;
