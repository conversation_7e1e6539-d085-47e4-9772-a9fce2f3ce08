# React Native Insurance Module - Complete Screen Mapping

## Overview
This document provides a comprehensive mapping of all 50+ native Android insurance screens to their React Native equivalents, ensuring complete feature parity and identical user experience while demonstrating React Native's strategic value.

## 🏗️ **Architecture Overview**

### Current Native Structure
- **Single Stepper Activity**: `InsuranceStepperActivity` hosts all product flows
- **Product-Specific Fragments**: Each step is a separate fragment (50+ fragments)
- **Navigation Targets**: 80+ navigation targets define flow between screens
- **Common Components**: Shared UI elements and utilities

### React Native Structure
```
react-native/src/
├── components/           # Reusable UI components
│   ├── common/          # Shared components (buttons, inputs, cards)
│   ├── forms/           # Form components (stepper, validation)
│   └── insurance/       # Insurance-specific components
├── screens/             # Screen components (50+ screens)
│   ├── dashboard/       # Main dashboard screens (4 screens)
│   ├── funeral/         # Funeral insurance screens (8 screens)
│   ├── life/           # Life insurance screens (8 screens)
│   ├── personal-lines/ # Personal lines screens (12 screens)
│   ├── vvap/           # VVAP screens (10 screens)
│   ├── hoc/            # Homeowner's cover screens (6 screens)
│   ├── claims/         # Claims processing screens (8 screens)
│   ├── policies/       # Policy management screens (6 screens)
│   └── common/         # Common screens (8 screens)
├── navigation/         # Navigation configuration
│   ├── AppNavigator.tsx     # Main app navigator
│   ├── InsuranceNavigator.tsx # Insurance module navigator
│   └── ProductNavigators.tsx  # Product-specific navigators
├── services/          # API services
│   ├── api/           # API clients
│   ├── storage/       # Local storage
│   └── analytics/     # Analytics tracking
├── utils/             # Utilities
│   ├── validation/    # Form validation
│   ├── formatting/    # Data formatting
│   └── constants/     # App constants
└── types/             # TypeScript types
    ├── api/           # API response types
    ├── navigation/    # Navigation types
    └── insurance/     # Insurance domain types
```

## 📱 **Complete Screen Mapping (50+ Screens)**

### 1. **Dashboard & Main Navigation (4 screens)**

| Native Screen | React Native Component | Status | Priority |
|---------------|----------------------|--------|----------|
| `InsuranceDashboardActivity` | `screens/dashboard/InsuranceDashboard.tsx` | ✅ **COMPLETED** | **HIGH** |
| `PolicyListContainerActivity` | `screens/policies/PolicyListContainer.tsx` | 🔄 **Phase 1** | **HIGH** |
| `InsuranceManageClaimListActivity` | `screens/claims/ClaimsList.tsx` | 📋 **Phase 2** | **MEDIUM** |
| `InsuranceEmergencyServicesActivity` | `screens/dashboard/EmergencyServices.tsx` | 🔄 **Phase 1** | **HIGH** |

### 2. **Funeral Insurance (NIFP) Flow (8 screens)**

| Native Fragment | React Native Screen | Navigation Route | Status | Priority |
|-----------------|-------------------|------------------|--------|----------|
| `FuneralPlanSetUpFragment` | `screens/funeral/PlanSetup.tsx` | `/funeral/setup` | 🔄 **Phase 1** | **HIGH** |
| `FuneralBeneficiaryFragment` | `screens/funeral/Beneficiary.tsx` | `/funeral/beneficiary` | 🔄 **Phase 1** | **HIGH** |
| `FuneralDebitOrderFragment` | `screens/funeral/DebitOrder.tsx` | `/funeral/debit-order` | 🔄 **Phase 1** | **HIGH** |
| `FuneralEmailFragment` | `screens/funeral/Email.tsx` | `/funeral/email` | 🔄 **Phase 1** | **HIGH** |
| `FuneralReviewQuoteFragment` | `screens/funeral/ReviewQuote.tsx` | `/funeral/review` | 🔄 **Phase 1** | **HIGH** |
| `FuneralDoneActivity` | `screens/funeral/Success.tsx` | `/funeral/success` | 🔄 **Phase 1** | **HIGH** |
| `FuneralAddDependentActivity` | `screens/funeral/AddDependent.tsx` | `/funeral/add-dependent` | 📋 **Phase 2** | **MEDIUM** |
| `FuneralClaimDoneActivity` | `screens/funeral/ClaimSuccess.tsx` | `/funeral/claim-success` | 📋 **Phase 3** | **LOW** |

### 3. **Life Insurance (MyCover Life) Flow (8 screens)**

| Native Fragment | React Native Screen | Navigation Route | Status | Priority |
|-----------------|-------------------|------------------|--------|----------|
| `MyCoverAboutYourselfFragment` | `screens/life/AboutYourself.tsx` | `/life/about` | 📋 **Phase 2** | **HIGH** |
| `MyCoverQuestionsFragment` | `screens/life/Questions.tsx` | `/life/questions` | 📋 **Phase 2** | **HIGH** |
| `MyCoverLifeCoverAmountFragment` | `screens/life/CoverAmount.tsx` | `/life/cover-amount` | 📋 **Phase 2** | **HIGH** |
| `MyCoverBeneficiaryFragment` | `screens/life/Beneficiary.tsx` | `/life/beneficiary` | 📋 **Phase 2** | **HIGH** |
| `MyCoverReviewQuoteFragment` | `screens/life/ReviewQuote.tsx` | `/life/review` | 📋 **Phase 2** | **HIGH** |
| `MyCoverNedbankDebitFragment` | `screens/life/DebitOrder.tsx` | `/life/debit-order` | 📋 **Phase 2** | **HIGH** |
| `MyCoverEmailFragment` | `screens/life/Email.tsx` | `/life/email` | 📋 **Phase 2** | **HIGH** |
| `MyCoverDoneActivity` | `screens/life/Success.tsx` | `/life/success` | 📋 **Phase 2** | **HIGH** |

### 4. **Personal Lines (MyCover) Flow (12 screens)**

| Native Fragment | React Native Screen | Navigation Route | Status | Priority |
|-----------------|-------------------|------------------|--------|----------|
| `PLBuildCoverFragment` | `screens/personal-lines/BuildCover.tsx` | `/pl/build-cover` | 📋 **Phase 2** | **HIGH** |
| `PLHistoryQuestionsFragment` | `screens/personal-lines/HistoryQuestions.tsx` | `/pl/history` | 📋 **Phase 2** | **MEDIUM** |
| `PLSelectPropertyFragment` | `screens/personal-lines/SelectProperty.tsx` | `/pl/property` | 📋 **Phase 2** | **HIGH** |
| `PLVehicleDetailsFragment` | `screens/personal-lines/VehicleDetails.tsx` | `/pl/vehicle` | 📋 **Phase 2** | **HIGH** |
| `PLDriverInformationFragment` | `screens/personal-lines/DriverInfo.tsx` | `/pl/driver` | 📋 **Phase 2** | **MEDIUM** |
| `PLReviewQuoteFragment` | `screens/personal-lines/ReviewQuote.tsx` | `/pl/review` | 📋 **Phase 2** | **HIGH** |
| `PLFinaliseQuoteFragment` | `screens/personal-lines/FinaliseQuote.tsx` | `/pl/finalise` | 📋 **Phase 2** | **HIGH** |
| `PLNedbankDebitFragment` | `screens/personal-lines/DebitOrder.tsx` | `/pl/debit-order` | 📋 **Phase 2** | **HIGH** |
| `PLBasicExcessFragment` | `screens/personal-lines/BasicExcess.tsx` | `/pl/excess` | 📋 **Phase 3** | **MEDIUM** |
| `PLAddedValuablesFragment` | `screens/personal-lines/AddedValuables.tsx` | `/pl/valuables` | 📋 **Phase 3** | **MEDIUM** |
| `PLEmailFragment` | `screens/personal-lines/Email.tsx` | `/pl/email` | 📋 **Phase 2** | **HIGH** |
| `PLBuildProductActivity` | `screens/personal-lines/BuildProduct.tsx` | `/pl/build-product` | 📋 **Phase 3** | **LOW** |

### 3. **Life Insurance (MyCover Life) Flow**

| Native Fragment | React Native Screen | Navigation Route |
|-----------------|-------------------|------------------|
| `MyCoverAboutYourselfFragment` | `screens/life/AboutYourself.tsx` | `/life/about` |
| `MyCoverQuestionsFragment` | `screens/life/Questions.tsx` | `/life/questions` |
| `MyCoverLifeCoverAmountFragment` | `screens/life/CoverAmount.tsx` | `/life/cover-amount` |
| `MyCoverBeneficiaryFragment` | `screens/life/Beneficiary.tsx` | `/life/beneficiary` |
| `MyCoverReviewQuoteFragment` | `screens/life/ReviewQuote.tsx` | `/life/review` |
| `MyCoverNedbankDebitFragment` | `screens/life/DebitOrder.tsx` | `/life/debit-order` |
| `MyCoverEmailFragment` | `screens/life/Email.tsx` | `/life/email` |
| `MyCoverDoneActivity` | `screens/life/Success.tsx` | `/life/success` |

### 4. **Personal Lines (MyCover) Flow**

| Native Fragment | React Native Screen | Navigation Route |
|-----------------|-------------------|------------------|
| `PLBuildCoverFragment` | `screens/personal-lines/BuildCover.tsx` | `/pl/build-cover` |
| `PLHistoryQuestionsFragment` | `screens/personal-lines/HistoryQuestions.tsx` | `/pl/history` |
| `PLSelectPropertyFragment` | `screens/personal-lines/SelectProperty.tsx` | `/pl/property` |
| `PLVehicleDetailsFragment` | `screens/personal-lines/VehicleDetails.tsx` | `/pl/vehicle` |
| `PLDriverInformationFragment` | `screens/personal-lines/DriverInfo.tsx` | `/pl/driver` |
| `PLReviewQuoteFragment` | `screens/personal-lines/ReviewQuote.tsx` | `/pl/review` |
| `PLFinaliseQuoteFragment` | `screens/personal-lines/FinaliseQuote.tsx` | `/pl/finalise` |
| `PLNedbankDebitFragment` | `screens/personal-lines/DebitOrder.tsx` | `/pl/debit-order` |

### 5. **VVAP (Vehicle & Asset Protection) Flow (10 screens)**

| Native Fragment | React Native Screen | Navigation Route | Status | Priority |
|-----------------|-------------------|------------------|--------|----------|
| `VVAPProductFragment` | `screens/vvap/ProductSelection.tsx` | `/vvap/products` | 📋 **Phase 3** | **MEDIUM** |
| `VVAPMoreAboutYourCarFragment` | `screens/vvap/VehicleDetails.tsx` | `/vvap/vehicle` | 📋 **Phase 3** | **MEDIUM** |
| `VVAPReviewQuoteFragment` | `screens/vvap/ReviewQuote.tsx` | `/vvap/review` | 📋 **Phase 3** | **MEDIUM** |
| `VVAPInsurableEventFragment` | `screens/vvap/InsurableEvent.tsx` | `/vvap/event` | 📋 **Phase 3** | **LOW** |
| `VVAPConfirmDetailsFragment` | `screens/vvap/ConfirmDetails.tsx` | `/vvap/confirm` | 📋 **Phase 3** | **LOW** |
| `VVAPUploadVehiclePhotosFragment` | `screens/vvap/UploadPhotos.tsx` | `/vvap/photos` | 📋 **Phase 3** | **LOW** |
| `VVAPVehicleTyresFragment` | `screens/vvap/VehicleTyres.tsx` | `/vvap/tyres` | 📋 **Phase 3** | **LOW** |
| `VVAPYourWarrantyOptionFragment` | `screens/vvap/WarrantyOption.tsx` | `/vvap/warranty` | 📋 **Phase 3** | **LOW** |
| `VVAPGAPAdditionalDetailActivity` | `screens/vvap/GAPDetails.tsx` | `/vvap/gap-details` | 📋 **Phase 3** | **LOW** |
| `VVAPTLCAdditionalDetailActivity` | `screens/vvap/TLCDetails.tsx` | `/vvap/tlc-details` | 📋 **Phase 3** | **LOW** |

### 6. **HOC (Homeowner's Cover) Flow (6 screens)**

| Native Fragment | React Native Screen | Navigation Route | Status | Priority |
|-----------------|-------------------|------------------|--------|----------|
| `HOCSelectYourPropertyFragment` | `screens/hoc/SelectProperty.tsx` | `/hoc/property` | 📋 **Phase 3** | **MEDIUM** |
| `HOCAboutGeyserDetailsFragment` | `screens/hoc/GeyserDetails.tsx` | `/hoc/geyser` | 📋 **Phase 3** | **LOW** |
| `HOCReviewPropertyFragment` | `screens/hoc/ReviewProperty.tsx` | `/hoc/review` | 📋 **Phase 3** | **MEDIUM** |
| `HOCEmailReportFragment` | `screens/hoc/Email.tsx` | `/hoc/email` | 📋 **Phase 3** | **MEDIUM** |
| `HOCDoneActivity` | `screens/hoc/Success.tsx` | `/hoc/success` | 📋 **Phase 3** | **MEDIUM** |
| `HOCConstructionTypeActivity` | `screens/hoc/ConstructionType.tsx` | `/hoc/construction` | 📋 **Phase 3** | **LOW** |

### 7. **Claims Processing (8 screens)**

| Native Screen | React Native Screen | Navigation Route | Status | Priority |
|---------------|-------------------|------------------|--------|----------|
| `PolicyAndClaimActivity` | `screens/claims/PolicyClaim.tsx` | `/claims/policy` | 📋 **Phase 2** | **HIGH** |
| `TrackClaimActivity` | `screens/claims/TrackClaim.tsx` | `/claims/track` | 📋 **Phase 2** | **HIGH** |
| `TrackClaimDetailsActivity` | `screens/claims/ClaimDetails.tsx` | `/claims/details` | 📋 **Phase 2** | **MEDIUM** |
| `PendingClaimActivity` | `screens/claims/PendingClaim.tsx` | `/claims/pending` | 📋 **Phase 2** | **MEDIUM** |
| `PolicyClaimFragment` | `screens/claims/PolicyClaimForm.tsx` | `/claims/form` | 📋 **Phase 2** | **HIGH** |
| `FuneralClaimAccountFragment` | `screens/claims/FuneralClaimAccount.tsx` | `/claims/funeral-account` | 📋 **Phase 3** | **LOW** |
| `InsuranceClaimThankYouActivity` | `screens/claims/ClaimThankYou.tsx` | `/claims/thank-you` | 📋 **Phase 2** | **MEDIUM** |
| `TrackProgressLineActivity` | `screens/claims/TrackProgress.tsx` | `/claims/progress` | 📋 **Phase 3** | **LOW** |

### 8. **Policy Management (6 screens)**

| Native Screen | React Native Screen | Navigation Route | Status | Priority |
|---------------|-------------------|------------------|--------|----------|
| `PolicyListFragment` | `screens/policies/PolicyList.tsx` | `/policies/list` | 🔄 **Phase 1** | **HIGH** |
| `PolicyDetailsFragment` | `screens/policies/PolicyDetails.tsx` | `/policies/details` | 🔄 **Phase 1** | **HIGH** |
| `ManagePolicyFragment` | `screens/policies/ManagePolicy.tsx` | `/policies/manage` | 📋 **Phase 2** | **HIGH** |
| `ApplicationListFragment` | `screens/policies/ApplicationList.tsx` | `/policies/applications` | 📋 **Phase 2** | **MEDIUM** |
| `InsuranceDetailsContainerActivity` | `screens/policies/DetailsContainer.tsx` | `/policies/container` | 📋 **Phase 2** | **MEDIUM** |
| `PendingPhotoListActivity` | `screens/policies/PendingPhotos.tsx` | `/policies/pending-photos` | 📋 **Phase 3** | **LOW** |

### 9. **Common/Shared Screens (8 screens)**

| Native Screen | React Native Screen | Navigation Route | Status | Priority |
|---------------|-------------------|------------------|--------|----------|
| `InsEducationActivity` | `screens/common/Education.tsx` | `/common/education` | 🔄 **Phase 1** | **HIGH** |
| `InsuranceTermsAndConditionsActivity` | `screens/common/TermsConditions.tsx` | `/common/terms` | 📋 **Phase 2** | **MEDIUM** |
| `InsuranceErrorActivity` | `screens/common/Error.tsx` | `/common/error` | 🔄 **Phase 1** | **HIGH** |
| `InsuranceThankYouActivity` | `screens/common/ThankYou.tsx` | `/common/thank-you` | 🔄 **Phase 1** | **HIGH** |
| `LeavingSoSoonActivity` | `screens/common/LeavingSoon.tsx` | `/common/leaving` | 📋 **Phase 2** | **MEDIUM** |
| `InsuranceSelectionListActivity` | `screens/common/SelectionList.tsx` | `/common/selection` | 📋 **Phase 2** | **MEDIUM** |
| `InsuranceSearchSelectionListActivity` | `screens/common/SearchSelection.tsx` | `/common/search` | 📋 **Phase 2** | **MEDIUM** |
| `ProductRestrictionActivity` | `screens/common/ProductRestriction.tsx` | `/common/restriction` | 📋 **Phase 3** | **LOW** |

## 🚀 **POC Implementation Strategy**

### **Phase 1: Foundation & High-Impact Demo (Week 1-2)**
**Goal**: Create compelling stakeholder demonstration

#### ✅ **Completed**
- **Insurance Dashboard** - Main entry point with pixel-perfect design

#### 🔄 **In Progress - Phase 1A**
- **Navigation Structure** - React Navigation with native-like transitions
- **Component Library** - Shared UI components matching current design system
- **Funeral Insurance Flow** - Complete 6-screen user journey (highest impact)
- **Policy List** - Show existing policies with real data

#### � **Phase 1B - Demo Enhancement**
- **Emergency Services** - Simple but visually impressive screen
- **Education Screens** - Rich content display capabilities
- **Error Handling** - Professional error states
- **Loading States** - Smooth loading experiences

### **Phase 2: Core Product Flows (Week 3-6)**
**Goal**: Demonstrate React Native's capability for complex flows

#### **Life Insurance Flow** (8 screens)
- Complete user journey from quote to purchase
- Complex form validation and state management
- Integration with existing APIs

#### **Personal Lines Flow** (12 screens)
- Most complex product flow
- Vehicle and property selection
- Multi-step form with conditional logic

#### **Claims Processing** (8 screens)
- High-value user functionality
- Photo upload and document management
- Real-time status tracking

### **Phase 3: Advanced Features (Week 7-10)**
**Goal**: Show React Native handling specialized insurance features

#### **VVAP Products** (10 screens)
- Vehicle-specific insurance products
- Photo upload and validation
- Complex product configuration

#### **HOC Products** (6 screens)
- Property insurance flows
- Address validation and mapping
- Construction type selection

### **Phase 4: Production Readiness (Week 11-12)**
**Goal**: Demonstrate enterprise-ready React Native implementation

#### **Performance Optimization**
- Bundle size optimization
- Memory management
- Smooth animations and transitions

#### **Integration & Testing**
- Full API integration
- Comprehensive error handling
- Unit and integration tests

## 📊 **Progress Tracking & Metrics**

### **Current Status**
- ✅ **Completed**: 1/70 screens (1.4%)
- 🔄 **In Progress**: 8/70 screens (11.4%) - Phase 1A
- 📋 **Planned**: 61/70 screens (87.2%)

### **Phase 1A Deliverables (Week 1-2)**
| Component | Status | Impact | Demo Value |
|-----------|--------|--------|------------|
| Insurance Dashboard | ✅ **DONE** | **HIGH** | ⭐⭐⭐⭐⭐ |
| Navigation Structure | 🔄 **50%** | **HIGH** | ⭐⭐⭐⭐⭐ |
| Funeral Plan Setup | 🔄 **25%** | **HIGH** | ⭐⭐⭐⭐⭐ |
| Funeral Beneficiary | 🔄 **0%** | **HIGH** | ⭐⭐⭐⭐ |
| Funeral Review Quote | 🔄 **0%** | **HIGH** | ⭐⭐⭐⭐⭐ |
| Policy List | 🔄 **0%** | **HIGH** | ⭐⭐⭐⭐ |
| Emergency Services | 🔄 **0%** | **MEDIUM** | ⭐⭐⭐ |
| Component Library | 🔄 **30%** | **HIGH** | ⭐⭐⭐⭐ |

### **Success Metrics & KPIs**

#### **Technical Excellence**
- **Visual Parity**: 100% pixel-perfect match to native screens
- **Performance**: Load times ≤ native implementation
- **Code Quality**: TypeScript coverage >95%, ESLint compliance
- **Test Coverage**: Unit tests >80%, E2E tests for critical flows

#### **Business Impact**
- **Development Efficiency**: 40% reduction in development time vs dual native
- **Maintenance Cost**: 60% reduction in maintenance overhead
- **Feature Velocity**: 50% faster feature delivery
- **Cross-Platform Consistency**: 100% identical UX across platforms

#### **User Experience**
- **Navigation Smoothness**: <16ms frame times, 60fps animations
- **Error Handling**: Graceful degradation, informative error messages
- **Accessibility**: WCAG 2.1 AA compliance
- **Offline Support**: Core functionality available offline

### **Stakeholder Demo Checklist**

#### **Visual Impact** ⭐⭐⭐⭐⭐
- [ ] Side-by-side comparison with native app
- [ ] Identical visual design and animations
- [ ] Smooth navigation transitions
- [ ] Professional loading states

#### **Functional Completeness** ⭐⭐⭐⭐
- [ ] Complete funeral insurance quote flow
- [ ] Policy list with real data
- [ ] Form validation and error handling
- [ ] Emergency services information

#### **Technical Demonstration** ⭐⭐⭐⭐⭐
- [ ] Hot reload development workflow
- [ ] Shared codebase benefits
- [ ] Performance monitoring
- [ ] Cross-platform code sharing metrics

#### **Business Case** ⭐⭐⭐⭐⭐
- [ ] Development time comparisons
- [ ] Maintenance cost projections
- [ ] Feature delivery velocity gains
- [ ] Team efficiency improvements

## 🎯 **Strategic Value Proposition**

### **For Stakeholders**
1. **Cost Reduction**: 60% less development and maintenance cost
2. **Faster Time-to-Market**: Single codebase = faster feature delivery
3. **Consistent UX**: Identical experience across Android and iOS
4. **Future-Proof**: Modern technology stack with strong ecosystem

### **For Development Teams**
1. **Single Codebase**: One team maintains both platforms
2. **Modern Tooling**: Hot reload, debugging, testing tools
3. **Shared Knowledge**: JavaScript/TypeScript skills across team
4. **Faster Iteration**: Immediate feedback and rapid prototyping

### **For Users**
1. **Identical Experience**: No difference from current native app
2. **Faster Updates**: Quicker bug fixes and feature releases
3. **Better Quality**: Shared code = fewer platform-specific bugs
4. **Enhanced Features**: Easier to add cross-platform capabilities

## 📈 **ROI Projections**

### **Development Cost Savings**
- **Year 1**: 40% reduction in development costs
- **Year 2**: 60% reduction in maintenance costs
- **Year 3**: 50% faster feature delivery

### **Team Efficiency Gains**
- **Single Team**: Instead of separate Android/iOS teams
- **Shared Expertise**: Knowledge transfer across platforms
- **Reduced Coordination**: No need to sync between platform teams

**Total Screens to Implement: 70 screens across 9 categories**
**Estimated Development Time: 12 weeks for complete implementation**
**Expected ROI: 300% within 18 months**
