# React Native Insurance Module - Complete Screen Mapping

## Overview
This document maps all current native Android insurance screens to their React Native equivalents, ensuring complete feature parity and identical user experience.

## 🏗️ **Architecture Overview**

### Current Native Structure
- **Single Stepper Activity**: `InsuranceStepperActivity` hosts all product flows
- **Product-Specific Fragments**: Each step is a separate fragment
- **Navigation Targets**: Define flow between screens
- **Common Components**: Shared UI elements and utilities

### React Native Structure
```
react-native/src/
├── components/           # Reusable UI components
├── screens/             # Screen components
│   ├── dashboard/       # Main dashboard screens
│   ├── funeral/         # Funeral insurance screens
│   ├── life/           # Life insurance screens  
│   ├── personal-lines/ # Personal lines screens
│   ├── vvap/           # VVAP screens
│   ├── hoc/            # Homeowner's cover screens
│   ├── claims/         # Claims processing screens
│   ├── policies/       # Policy management screens
│   └── common/         # Common screens (education, etc.)
├── navigation/         # Navigation configuration
├── services/          # API services
├── utils/             # Utilities
└── types/             # TypeScript types
```

## 📱 **Screen Mapping by Category**

### 1. **Dashboard & Main Navigation**

| Native Screen | React Native Component | Description |
|---------------|----------------------|-------------|
| `InsuranceDashboardActivity` | `screens/dashboard/InsuranceDashboard.tsx` | ✅ **COMPLETED** - Main insurance dashboard |
| `PolicyListContainerActivity` | `screens/policies/PolicyListContainer.tsx` | Policy management hub |
| `InsuranceManageClaimListActivity` | `screens/claims/ClaimsList.tsx` | Claims management |
| `InsuranceEmergencyServicesActivity` | `screens/dashboard/EmergencyServices.tsx` | Emergency contacts |

### 2. **Funeral Insurance (NIFP) Flow**

| Native Fragment | React Native Screen | Navigation Route |
|-----------------|-------------------|------------------|
| `FuneralPlanSetUpFragment` | `screens/funeral/PlanSetup.tsx` | `/funeral/setup` |
| `FuneralBeneficiaryFragment` | `screens/funeral/Beneficiary.tsx` | `/funeral/beneficiary` |
| `FuneralDebitOrderFragment` | `screens/funeral/DebitOrder.tsx` | `/funeral/debit-order` |
| `FuneralEmailFragment` | `screens/funeral/Email.tsx` | `/funeral/email` |
| `FuneralReviewQuoteFragment` | `screens/funeral/ReviewQuote.tsx` | `/funeral/review` |
| `FuneralDoneActivity` | `screens/funeral/Success.tsx` | `/funeral/success` |

### 3. **Life Insurance (MyCover Life) Flow**

| Native Fragment | React Native Screen | Navigation Route |
|-----------------|-------------------|------------------|
| `MyCoverAboutYourselfFragment` | `screens/life/AboutYourself.tsx` | `/life/about` |
| `MyCoverQuestionsFragment` | `screens/life/Questions.tsx` | `/life/questions` |
| `MyCoverLifeCoverAmountFragment` | `screens/life/CoverAmount.tsx` | `/life/cover-amount` |
| `MyCoverBeneficiaryFragment` | `screens/life/Beneficiary.tsx` | `/life/beneficiary` |
| `MyCoverReviewQuoteFragment` | `screens/life/ReviewQuote.tsx` | `/life/review` |
| `MyCoverNedbankDebitFragment` | `screens/life/DebitOrder.tsx` | `/life/debit-order` |
| `MyCoverEmailFragment` | `screens/life/Email.tsx` | `/life/email` |
| `MyCoverDoneActivity` | `screens/life/Success.tsx` | `/life/success` |

### 4. **Personal Lines (MyCover) Flow**

| Native Fragment | React Native Screen | Navigation Route |
|-----------------|-------------------|------------------|
| `PLBuildCoverFragment` | `screens/personal-lines/BuildCover.tsx` | `/pl/build-cover` |
| `PLHistoryQuestionsFragment` | `screens/personal-lines/HistoryQuestions.tsx` | `/pl/history` |
| `PLSelectPropertyFragment` | `screens/personal-lines/SelectProperty.tsx` | `/pl/property` |
| `PLVehicleDetailsFragment` | `screens/personal-lines/VehicleDetails.tsx` | `/pl/vehicle` |
| `PLDriverInformationFragment` | `screens/personal-lines/DriverInfo.tsx` | `/pl/driver` |
| `PLReviewQuoteFragment` | `screens/personal-lines/ReviewQuote.tsx` | `/pl/review` |
| `PLFinaliseQuoteFragment` | `screens/personal-lines/FinaliseQuote.tsx` | `/pl/finalise` |
| `PLNedbankDebitFragment` | `screens/personal-lines/DebitOrder.tsx` | `/pl/debit-order` |

### 5. **VVAP (Vehicle & Asset Protection) Flow**

| Native Fragment | React Native Screen | Navigation Route |
|-----------------|-------------------|------------------|
| `VVAPProductFragment` | `screens/vvap/ProductSelection.tsx` | `/vvap/products` |
| `VVAPMoreAboutYourCarFragment` | `screens/vvap/VehicleDetails.tsx` | `/vvap/vehicle` |
| `VVAPReviewQuoteFragment` | `screens/vvap/ReviewQuote.tsx` | `/vvap/review` |
| `VVAPInsurableEventFragment` | `screens/vvap/InsurableEvent.tsx` | `/vvap/event` |
| `VVAPConfirmDetailsFragment` | `screens/vvap/ConfirmDetails.tsx` | `/vvap/confirm` |

### 6. **HOC (Homeowner's Cover) Flow**

| Native Fragment | React Native Screen | Navigation Route |
|-----------------|-------------------|------------------|
| `HOCSelectYourPropertyFragment` | `screens/hoc/SelectProperty.tsx` | `/hoc/property` |
| `HOCAboutGeyserDetailsFragment` | `screens/hoc/GeyserDetails.tsx` | `/hoc/geyser` |
| `HOCReviewPropertyFragment` | `screens/hoc/ReviewProperty.tsx` | `/hoc/review` |
| `HOCEmailReportFragment` | `screens/hoc/Email.tsx` | `/hoc/email` |
| `HOCDoneActivity` | `screens/hoc/Success.tsx` | `/hoc/success` |

### 7. **Claims Processing**

| Native Screen | React Native Screen | Navigation Route |
|---------------|-------------------|------------------|
| `PolicyAndClaimActivity` | `screens/claims/PolicyClaim.tsx` | `/claims/policy` |
| `TrackClaimActivity` | `screens/claims/TrackClaim.tsx` | `/claims/track` |
| `TrackClaimDetailsActivity` | `screens/claims/ClaimDetails.tsx` | `/claims/details` |
| `PendingClaimActivity` | `screens/claims/PendingClaim.tsx` | `/claims/pending` |

### 8. **Policy Management**

| Native Screen | React Native Screen | Navigation Route |
|---------------|-------------------|------------------|
| `PolicyListFragment` | `screens/policies/PolicyList.tsx` | `/policies/list` |
| `PolicyDetailsFragment` | `screens/policies/PolicyDetails.tsx` | `/policies/details` |
| `ManagePolicyFragment` | `screens/policies/ManagePolicy.tsx` | `/policies/manage` |
| `ApplicationListFragment` | `screens/policies/ApplicationList.tsx` | `/policies/applications` |

### 9. **Common/Shared Screens**

| Native Screen | React Native Screen | Navigation Route |
|---------------|-------------------|------------------|
| `InsEducationActivity` | `screens/common/Education.tsx` | `/common/education` |
| `InsuranceTermsAndConditionsActivity` | `screens/common/TermsConditions.tsx` | `/common/terms` |
| `InsuranceErrorActivity` | `screens/common/Error.tsx` | `/common/error` |
| `InsuranceThankYouActivity` | `screens/common/ThankYou.tsx` | `/common/thank-you` |
| `LeavingSoSoonActivity` | `screens/common/LeavingSoon.tsx` | `/common/leaving` |

## 🚀 **Implementation Phases**

### Phase 1: Foundation (Week 1-2)
- ✅ **Dashboard** - Main insurance dashboard (COMPLETED)
- 🔄 **Navigation Setup** - React Navigation configuration
- 🔄 **Common Components** - Shared UI components
- 🔄 **API Services** - Service layer setup

### Phase 2: Core Products (Week 3-6)
- 🔄 **Funeral Insurance** - Complete NIFP flow (8 screens)
- 🔄 **Life Insurance** - Complete MCL flow (8 screens)
- 🔄 **Personal Lines** - Complete PL flow (8 screens)

### Phase 3: Advanced Products (Week 7-10)
- 🔄 **VVAP Products** - Vehicle protection flows (5 screens)
- 🔄 **HOC Products** - Homeowner's cover (5 screens)
- 🔄 **Claims Processing** - Claims management (4 screens)

### Phase 4: Management & Polish (Week 11-12)
- 🔄 **Policy Management** - Policy administration (4 screens)
- 🔄 **Error Handling** - Comprehensive error states
- 🔄 **Performance** - Optimization and testing

## 📊 **Progress Tracking**

### Completed ✅
- **Insurance Dashboard** - Main entry point with exact visual match

### In Progress 🔄
- **Navigation Structure** - React Navigation setup
- **Component Library** - Shared UI components

### Planned 📋
- **Product Flows** - Individual insurance product journeys
- **API Integration** - Backend service connections
- **Testing** - Unit and integration tests

## 🎯 **Success Metrics**

1. **Visual Parity** - 100% identical to current native screens
2. **Feature Completeness** - All 50+ screens implemented
3. **Performance** - Load times ≤ current native implementation
4. **User Experience** - Zero difference in user journey
5. **Code Quality** - TypeScript, testing, documentation

**Total Screens to Implement: 50+ screens across 9 categories**
