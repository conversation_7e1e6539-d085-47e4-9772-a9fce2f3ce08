import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from './navigation/AppNavigator';
import Icon from 'react-native-vector-icons/MaterialIcons';

type NavigationProp = StackNavigationProp<RootStackParamList>;

const InsuranceDashboard: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();

  const showReactNativeAlert = (feature: string) => {
    Alert.alert(
      'React Native POC',
      `${feature} - This demonstrates React Native functionality in the insurance module`,
      [{ text: 'OK' }]
    );
  };

  const handleManagePoliciesPress = () => {
    navigation.navigate('PolicyList');
  };

  const handleCompleteApplicationsPress = () => {
    Alert.alert(
      'React Native POC',
      'Complete applications - This would show pending applications and allow users to complete them. Demonstrates React Native\'s capability for complex form flows.',
      [{ text: 'OK' }]
    );
  };

  const handleEmergencyServicesPress = () => {
    navigation.navigate('EmergencyServices');
  };

  const handleForYouPress = () => {
    Alert.alert(
      'React Native POC',
      'For you offers - This would show personalized insurance offers based on user profile and behavior. Demonstrates React Native\'s data-driven UI capabilities.',
      [{ text: 'OK' }]
    );
  };

  const handleGetCoverPress = (productType: string) => {
    if (productType === 'Funeral') {
      navigation.navigate('FuneralFlow');
    } else if (productType === 'Life') {
      Alert.alert(
        'React Native POC',
        'Life cover - This would start the Life Insurance quote flow. The complete flow will be implemented in Phase 2 of the React Native migration.',
        [{ text: 'OK' }]
      );
    } else if (productType === 'Vehicle, home &\nvaluables') {
      Alert.alert(
        'React Native POC',
        'Personal Lines cover - This would start the Vehicle, Home & Valuables quote flow. This is the most complex insurance product with 12+ screens.',
        [{ text: 'OK' }]
      );
    } else if (productType === 'Legal expenses') {
      Alert.alert(
        'React Native POC',
        'Legal expenses cover - This would start the Legal Expenses insurance quote flow. Demonstrates React Native\'s ability to handle specialized insurance products.',
        [{ text: 'OK' }]
      );
    } else {
      showReactNativeAlert(`${productType} cover`);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header - matching exact toolbar style */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Insurance</Text>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* My Insurance Section - exact text and spacing */}
        <Text style={styles.sectionTitle}>My insurance</Text>

        {/* Manage Policies Section - exact text from dashboard_manage_policy_layout.xml */}
        <TouchableOpacity style={styles.actionCard} onPress={handleManagePoliciesPress}>
          <View style={styles.cardContent}>
            <Icon name="assignment" size={40} color="#00A651" style={styles.cardIcon} />
            <View style={styles.cardTextContainer}>
              <Text style={styles.cardTitle}>Policies and claims</Text>
              <Text style={styles.cardSubtitle}>Manage cover and submit your claims.</Text>
            </View>
            <Icon name="chevron-right" size={24} color="#999999" />
          </View>
        </TouchableOpacity>

        {/* Complete Applications Section - exact text */}
        <TouchableOpacity style={styles.actionCard} onPress={handleCompleteApplicationsPress}>
          <View style={styles.cardContent}>
            <Icon name="edit-note" size={40} color="#00A651" style={styles.cardIcon} />
            <View style={styles.cardTextContainer}>
              <Text style={styles.cardTitle}>Complete applications</Text>
              <Text style={styles.cardSubtitle}>Complete or update your quote.</Text>
            </View>
            <Icon name="chevron-right" size={24} color="#999999" />
          </View>
        </TouchableOpacity>

        {/* Emergency Services Section - exact text */}
        <TouchableOpacity style={styles.actionCard} onPress={handleEmergencyServicesPress}>
          <View style={styles.cardContent}>
            <Icon name="emergency" size={40} color="#00A651" style={styles.cardIcon} />
            <View style={styles.cardTextContainer}>
              <Text style={styles.cardTitle}>Emergency services</Text>
              <Text style={styles.cardSubtitle}>Information on whom to call when you have an emergency.</Text>
            </View>
            <Icon name="chevron-right" size={24} color="#999999" />
          </View>
        </TouchableOpacity>

        {/* Get Cover Section - exact text and spacing */}
        <Text style={styles.sectionTitle}>Get cover</Text>

        {/* For You Section - with notification badge */}
        <TouchableOpacity style={styles.forYouCard} onPress={handleForYouPress}>
          <View style={styles.cardContent}>
            <Icon name="star" size={40} color="#00A651" style={styles.cardIcon} />
            <View style={styles.cardTextContainer}>
              <Text style={styles.cardTitle}>For you</Text>
              <Text style={styles.cardSubtitle}>Your personalised offers.</Text>
            </View>
            <View style={styles.notificationBadge}>
              <Text style={styles.badgeText}>1</Text>
            </View>
            <Icon name="chevron-right" size={24} color="#999999" />
          </View>
        </TouchableOpacity>

        {/* Insurance Products Grid - 2x2 layout matching RecyclerView */}
        <View style={styles.productsGrid}>
          <View style={styles.productRow}>
            <TouchableOpacity
              style={styles.productCard}
              onPress={() => handleGetCoverPress('Funeral')}
            >
              <Icon name="favorite" size={48} color="#00A651" />
              <Text style={styles.productTitle}>Funeral</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.productCard}
              onPress={() => handleGetCoverPress('Life')}
            >
              <Icon name="security" size={48} color="#00A651" />
              <Text style={styles.productTitle}>Life</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.productRow}>
            <TouchableOpacity
              style={styles.productCard}
              onPress={() => handleGetCoverPress('Vehicle, home & valuables')}
            >
              <Icon name="home" size={48} color="#00A651" />
              <Text style={styles.productTitle}>Vehicle, home &{'\n'}valuables</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.productCard}
              onPress={() => handleGetCoverPress('Legal expenses')}
            >
              <Icon name="gavel" size={48} color="#00A651" />
              <Text style={styles.productTitle}>Legal expenses</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* React Native POC Notice */}
        <View style={styles.pocNotice}>
          <Icon name="info" size={20} color="#FF6B35" />
          <Text style={styles.pocText}>
            React Native POC - All functionality demonstrates React Native integration
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    paddingHorizontal: 15, // dimen_15dp margin
    paddingVertical: 16,
    backgroundColor: '#ffffff',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2d2323', // insurance_toolbar_title_color
  },
  scrollView: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 22, // dimen_22sp
    fontWeight: '400', // roboto_regular
    color: '#333333', // black_333333
    marginHorizontal: 15, // dimen_15dp
    marginTop: 20, // dimen_20dp
    marginBottom: 0,
  },
  actionCard: {
    backgroundColor: '#ffffff',
    marginHorizontal: 0,
    marginBottom: 0,
  },
  forYouCard: {
    backgroundColor: '#ffffff',
    marginHorizontal: 15, // dimen_15dp
    marginTop: 20, // dimen_20dp
    marginBottom: 0,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15, // dimen_15dp
    paddingVertical: 20, // dimen_20dp
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE', // divider_background
  },
  cardIcon: {
    width: 40,
    height: 40,
    marginRight: 10, // dimen_10dp
  },
  cardTextContainer: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 14, // dimen_14sp
    fontWeight: '500', // roboto_medium
    color: '#333333', // black_333333
    marginBottom: 5, // dimen_5dp
  },
  cardSubtitle: {
    fontSize: 14, // dimen_14sp
    fontWeight: '400', // roboto_regular
    color: '#333333', // black_333333
  },
  notificationBadge: {
    backgroundColor: '#00A651', // apple_green
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  badgeText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  productsGrid: {
    marginHorizontal: 10, // dimen_10dp
    marginTop: 5, // dimen_5dp
    marginBottom: 20, // dimen_20dp
  },
  productRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10, // dimen_10dp
  },
  productCard: {
    width: '48%', // Equal width for 2 columns
    height: 154, // dimen_154dp from insurance_dashboard_custom_cell.xml
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 10, // dimen_10dp
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    margin: 5, // dimen_5dp
  },
  productTitle: {
    fontSize: 16, // dimen_16sp
    fontWeight: '500', // roboto_medium
    color: '#333333', // black_333333
    marginTop: 10, // dimen_10dp
    textAlign: 'center',
  },
  pocNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    padding: 16,
    borderRadius: 8,
    marginHorizontal: 15,
    marginBottom: 20,
  },
  pocText: {
    fontSize: 14,
    color: '#FF6B35',
    marginLeft: 8,
    flex: 1,
  },
});

export default InsuranceDashboard;
