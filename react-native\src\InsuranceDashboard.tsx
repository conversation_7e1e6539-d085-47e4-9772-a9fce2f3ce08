import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const InsuranceDashboard: React.FC = () => {
  const handleManagePoliciesPress = () => {
    console.log('Manage Policies pressed');
  };

  const handleCompleteApplicationsPress = () => {
    console.log('Complete Applications pressed');
  };

  const handleEmergencyServicesPress = () => {
    console.log('Emergency Services pressed');
  };

  const handleGetCoverPress = (productType: string) => {
    console.log(`Get Cover pressed for: ${productType}`);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Insurance</Text>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* My Insurance Section */}
        <Text style={styles.sectionTitle}>My Insurance</Text>

        {/* Manage Policies Section */}
        <TouchableOpacity style={styles.actionCard} onPress={handleManagePoliciesPress}>
          <View style={styles.cardContent}>
            <Icon name="policy" size={24} color="#00A651" style={styles.cardIcon} />
            <View style={styles.cardTextContainer}>
              <Text style={styles.cardTitle}>Manage policies and claims</Text>
              <Text style={styles.cardSubtitle}>View and manage your existing policies</Text>
            </View>
            <Icon name="chevron-right" size={24} color="#666666" />
          </View>
        </TouchableOpacity>

        {/* Complete Applications Section */}
        <TouchableOpacity style={styles.actionCard} onPress={handleCompleteApplicationsPress}>
          <View style={styles.cardContent}>
            <Icon name="assignment" size={24} color="#00A651" style={styles.cardIcon} />
            <View style={styles.cardTextContainer}>
              <Text style={styles.cardTitle}>Complete applications</Text>
              <Text style={styles.cardSubtitle}>Finish your pending applications</Text>
            </View>
            <Icon name="chevron-right" size={24} color="#666666" />
          </View>
        </TouchableOpacity>

        {/* Emergency Services Section */}
        <TouchableOpacity style={styles.actionCard} onPress={handleEmergencyServicesPress}>
          <View style={styles.cardContent}>
            <Icon name="emergency" size={24} color="#00A651" style={styles.cardIcon} />
            <View style={styles.cardTextContainer}>
              <Text style={styles.cardTitle}>Emergency services</Text>
              <Text style={styles.cardSubtitle}>Access emergency assistance</Text>
            </View>
            <Icon name="chevron-right" size={24} color="#666666" />
          </View>
        </TouchableOpacity>

        {/* Divider */}
        <View style={styles.divider} />

        {/* Get Cover Section */}
        <Text style={styles.sectionTitle}>Get Cover</Text>

        {/* Insurance Products Grid */}
        <View style={styles.productsGrid}>
          <TouchableOpacity 
            style={styles.productCard} 
            onPress={() => handleGetCoverPress('Funeral Cover')}
          >
            <Icon name="favorite" size={32} color="#00A651" />
            <Text style={styles.productTitle}>Funeral Cover</Text>
            <Text style={styles.productSubtitle}>Protect your family</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.productCard} 
            onPress={() => handleGetCoverPress('Life Cover')}
          >
            <Icon name="security" size={32} color="#00A651" />
            <Text style={styles.productTitle}>Life Cover</Text>
            <Text style={styles.productSubtitle}>Secure your future</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.productCard} 
            onPress={() => handleGetCoverPress('Vehicle Cover')}
          >
            <Icon name="directions-car" size={32} color="#00A651" />
            <Text style={styles.productTitle}>Vehicle Cover</Text>
            <Text style={styles.productSubtitle}>Protect your vehicle</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.productCard} 
            onPress={() => handleGetCoverPress('Legal Expense')}
          >
            <Icon name="gavel" size={32} color="#00A651" />
            <Text style={styles.productTitle}>Legal Expense</Text>
            <Text style={styles.productSubtitle}>Legal protection</Text>
          </TouchableOpacity>
        </View>

        {/* POC Notice */}
        <View style={styles.pocNotice}>
          <Icon name="info" size={20} color="#FF6B35" />
          <Text style={styles.pocText}>
            This is a React Native POC. Functionality will be implemented in future iterations.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333333',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '400',
    color: '#333333',
    marginTop: 20,
    marginBottom: 16,
  },
  actionCard: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  cardIcon: {
    marginRight: 16,
  },
  cardTextContainer: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
  divider: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginVertical: 20,
  },
  productsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  productCard: {
    width: '48%',
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  productTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    marginTop: 8,
    textAlign: 'center',
  },
  productSubtitle: {
    fontSize: 12,
    color: '#666666',
    marginTop: 4,
    textAlign: 'center',
  },
  pocNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  pocText: {
    fontSize: 14,
    color: '#FF6B35',
    marginLeft: 8,
    flex: 1,
  },
});

export default InsuranceDashboard;
