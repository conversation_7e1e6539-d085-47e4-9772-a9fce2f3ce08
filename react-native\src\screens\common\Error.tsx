import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../../navigation/AppNavigator';

import Card from '../../components/common/Card';
import Button from '../../components/common/Button';

type RouteProp = RouteProp<RootStackParamList, 'Error'>;
type NavigationProp = StackNavigationProp<RootStackParamList, 'Error'>;

interface ErrorType {
  icon: string;
  title: string;
  description: string;
  actions: string[];
}

const Error: React.FC = () => {
  const route = useRoute<RouteProp>();
  const navigation = useNavigation<NavigationProp>();
  const { message = 'An unexpected error occurred', canRetry = true } = route.params || {};

  const getErrorType = (errorMessage: string): ErrorType => {
    const lowerMessage = errorMessage.toLowerCase();
    
    if (lowerMessage.includes('network') || lowerMessage.includes('connection')) {
      return {
        icon: '📡',
        title: 'Connection Problem',
        description: 'We\'re having trouble connecting to our servers. Please check your internet connection and try again.',
        actions: ['Check your internet connection', 'Try again in a few moments', 'Contact support if the problem persists'],
      };
    }
    
    if (lowerMessage.includes('timeout')) {
      return {
        icon: '⏱️',
        title: 'Request Timeout',
        description: 'The request is taking longer than expected. This might be due to high server load.',
        actions: ['Wait a moment and try again', 'Check your internet connection', 'Contact support if this continues'],
      };
    }
    
    if (lowerMessage.includes('validation') || lowerMessage.includes('invalid')) {
      return {
        icon: '⚠️',
        title: 'Validation Error',
        description: 'Some of the information provided is not valid. Please review and correct the highlighted fields.',
        actions: ['Review the form for errors', 'Ensure all required fields are completed', 'Check that information is in the correct format'],
      };
    }
    
    if (lowerMessage.includes('server') || lowerMessage.includes('500')) {
      return {
        icon: '🔧',
        title: 'Server Error',
        description: 'Our servers are experiencing technical difficulties. Our team has been notified and is working to resolve this.',
        actions: ['Try again in a few minutes', 'Check our status page for updates', 'Contact support if urgent'],
      };
    }
    
    // Default error type
    return {
      icon: '❌',
      title: 'Something went wrong',
      description: message,
      actions: ['Try the action again', 'Restart the app if the problem continues', 'Contact our support team for assistance'],
    };
  };

  const errorType = getErrorType(message);

  const handleRetry = () => {
    Alert.alert(
      'React Native POC',
      'This would retry the failed operation. React Native provides excellent error recovery and retry mechanisms.',
      [
        {
          text: 'Go Back',
          onPress: () => navigation.goBack(),
        },
      ]
    );
  };

  const handleContactSupport = () => {
    Alert.alert(
      'React Native POC',
      'This would open support options (phone, email, chat). React Native can integrate with device communication features seamlessly.',
      [{ text: 'OK' }]
    );
  };

  const handleGoHome = () => {
    navigation.navigate('InsuranceDashboard');
  };

  const handleReportProblem = () => {
    Alert.alert(
      'React Native POC',
      'This would open a problem reporting form. React Native can capture device information, logs, and user feedback for debugging.',
      [{ text: 'OK' }]
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Error Icon and Message */}
        <View style={styles.errorHeader}>
          <View style={styles.errorIcon}>
            <Text style={styles.errorIconText}>{errorType.icon}</Text>
          </View>
          <Text style={styles.errorTitle}>{errorType.title}</Text>
          <Text style={styles.errorDescription}>{errorType.description}</Text>
        </View>

        {/* What you can do */}
        <Card style={styles.actionsCard} variant="default" padding="large" margin="medium">
          <Text style={styles.actionsTitle}>What you can do:</Text>
          {errorType.actions.map((action, index) => (
            <View key={index} style={styles.actionItem}>
              <Text style={styles.actionBullet}>{index + 1}.</Text>
              <Text style={styles.actionText}>{action}</Text>
            </View>
          ))}
        </Card>

        {/* Error Details */}
        <Card style={styles.detailsCard} variant="outlined" padding="medium" margin="medium">
          <Text style={styles.detailsTitle}>Error Details</Text>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Error message:</Text>
            <Text style={styles.detailValue}>{message}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Time:</Text>
            <Text style={styles.detailValue}>{new Date().toLocaleString()}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Error ID:</Text>
            <Text style={styles.detailValue}>ERR-{Date.now().toString().slice(-6)}</Text>
          </View>
        </Card>

        {/* Support Information */}
        <Card style={styles.supportCard} variant="elevated" padding="large" margin="medium">
          <Text style={styles.supportTitle}>Need help?</Text>
          <Text style={styles.supportText}>
            Our support team is available 24/7 to assist you with any issues.
          </Text>
          
          <View style={styles.supportOptions}>
            <View style={styles.supportOption}>
              <Text style={styles.supportOptionIcon}>📞</Text>
              <View style={styles.supportOptionContent}>
                <Text style={styles.supportOptionTitle}>Call us</Text>
                <Text style={styles.supportOptionText}>0860 555 111</Text>
              </View>
            </View>
            
            <View style={styles.supportOption}>
              <Text style={styles.supportOptionIcon}>💬</Text>
              <View style={styles.supportOptionContent}>
                <Text style={styles.supportOptionTitle}>Live chat</Text>
                <Text style={styles.supportOptionText}>Available 24/7</Text>
              </View>
            </View>
            
            <View style={styles.supportOption}>
              <Text style={styles.supportOptionIcon}>📧</Text>
              <View style={styles.supportOptionContent}>
                <Text style={styles.supportOptionTitle}>Email</Text>
                <Text style={styles.supportOptionText}><EMAIL></Text>
              </View>
            </View>
          </View>
        </Card>

        {/* React Native POC Notice */}
        <Card style={styles.pocNotice} variant="flat" padding="medium" margin="medium">
          <View style={styles.pocContent}>
            <Text style={styles.pocIcon}>🔧</Text>
            <Text style={styles.pocText}>
              React Native POC - Professional error handling with user-friendly messaging and recovery options
            </Text>
          </View>
        </Card>
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.footer}>
        {canRetry && (
          <Button
            title="Try Again"
            onPress={handleRetry}
            variant="primary"
            fullWidth
            size="large"
            style={styles.primaryButton}
          />
        )}
        
        <View style={styles.secondaryButtons}>
          <Button
            title="Contact Support"
            onPress={handleContactSupport}
            variant="outline"
            size="medium"
            style={styles.secondaryButton}
          />
          
          <Button
            title="Go to Dashboard"
            onPress={handleGoHome}
            variant="text"
            size="medium"
            style={styles.secondaryButton}
          />
        </View>
        
        <Button
          title="Report Problem"
          onPress={handleReportProblem}
          variant="text"
          size="small"
          style={styles.reportButton}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  scrollView: {
    flex: 1,
  },
  errorHeader: {
    alignItems: 'center',
    padding: 40,
    paddingBottom: 20,
  },
  errorIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#fff5f5',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    borderWidth: 2,
    borderColor: '#fecaca',
  },
  errorIconText: {
    fontSize: 40,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333333',
    textAlign: 'center',
    marginBottom: 8,
  },
  errorDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
  },
  actionsCard: {
    backgroundColor: '#f8f9fa',
  },
  actionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  actionBullet: {
    fontSize: 14,
    fontWeight: '600',
    color: '#00A651',
    marginRight: 12,
    marginTop: 2,
    minWidth: 20,
  },
  actionText: {
    fontSize: 14,
    color: '#333333',
    flex: 1,
    lineHeight: 20,
  },
  detailsCard: {
    backgroundColor: '#fff9e6',
    borderColor: '#ffc107',
  },
  detailsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666666',
    width: 100,
  },
  detailValue: {
    fontSize: 14,
    color: '#333333',
    flex: 1,
    fontFamily: 'monospace',
  },
  supportCard: {
    backgroundColor: '#f0f8ff',
    borderColor: '#007bff',
    borderWidth: 1,
  },
  supportTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  supportText: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 20,
    lineHeight: 20,
  },
  supportOptions: {
    gap: 16,
  },
  supportOption: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  supportOptionIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  supportOptionContent: {
    flex: 1,
  },
  supportOptionTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 2,
  },
  supportOptionText: {
    fontSize: 14,
    color: '#666666',
  },
  pocNotice: {
    backgroundColor: '#FFF3E0',
    borderRadius: 8,
  },
  pocContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pocIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  pocText: {
    fontSize: 14,
    color: '#FF6B35',
    flex: 1,
    lineHeight: 20,
  },
  footer: {
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  primaryButton: {
    marginBottom: 12,
  },
  secondaryButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  secondaryButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  reportButton: {
    alignSelf: 'center',
  },
});

export default Error;
