// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext.kotlin_version = '1.9.22'

    repositories {
        google()
        mavenCentral()
        maven { url "https://jitpack.io" }
        maven { url "https://oss.sonatype.org/content/repositories/snapshots" }
        maven { url 'https://developer.huawei.com/repo/' }
        maven { url 'https://oss.jfrog.org/artifactory/oss-snapshot-local' }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath 'com.google.gms:google-services:4.3.14'
        classpath "com.hiya:jacoco-android:0.2"
        classpath 'com.huawei.agconnect:agcp:1.7.2.300'
        // Add the Crashlytics Gradle plugin.
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.5.2'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

plugins {
    id "org.sonarqube" version "4.4.1.3373"
    id 'org.jetbrains.kotlin.android' version '1.9.22' apply false
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://developer.huawei.com/repo/' }
        maven { url 'https://jitpack.io' }
        maven { url 'https://maven.microblink.com' }
        maven { url "https://oss.sonatype.org/content/repositories/snapshots" }
        maven { url "https://greenrobot.org/eventbus" }
        maven { url "..//TapAndPaySDK" }
        maven {
            url = uri("https://s3-us-west-2.amazonaws.com/si-mobile-sdks/android/")
        }
        flatDir {
            dirs 'libs', '../services/libs'
        }
        maven {
            url "${azureArtifactURL}"
            credentials {
                username "${azureArtifactUserName}"
                password System.getenv("AZURE_ARTIFACTS_ENV_ACCESS_TOKEN") ?: "${azureArtifactPassToken}"
            }
        }
        maven {
            url "${azureNedbankLimitedArtifactsURL}"
            credentials {
                username "${azureNedbankLimitedArtifacts}"
                password System.getenv("AZURE_ARTIFACTS_ENV_ACCESS_TOKEN") ?: "${azureArtifactPassToken}"
            }
        }
    }
}

tasks.register('clean', Delete) {
    delete rootProject.buildDir
}


// Inject Build VersionCode and Version Name
def getMyVersionCode = {
    def code = project.hasProperty('versionCode') ? versionCode.toInteger() : 274
    println "VersionCode is set to $code"
    return code
}

def getMyVersionName = {
    def name = project.hasProperty('versionName') ? versionName : "v9.5.3"
    def tag = name.split("v")
    println "VersionName is set to $tag"
    return tag[1]
}

// Define versions in a single place
ext {
    // updating min Sdk and tools
    minSdkVersion = 24
    targetSdkVersion = 34
    compileSdkVersion = 34
    // Different SDK's for MoneyApp.
    nidSdkVersion = '4.1.58'
    eficaSdkVersion = '1.2.19'
    uiSdkVersion = '1.2.5'
    fatcaSdkVersion = '0.0.16'
    zoomSdkVersion = "8.12.0"
    // version information
    versionCode = getMyVersionCode()
    versionName = getMyVersionName()
    googlePlayServicesVersion = '19.0.0'
    googlePlacesVersion = '3.5.0'
    buildToolsVersion = '30.0.2'
    constraintLayoutVersion = '2.1.4'
    firebaseCoreVersion = '21.1.1'
    firebaseMessagingVersion = '24.1.0'
    firebaseConfigVersion = '22.0.1'
    crashlyticsVersion = '2.10.1'
    leakCanaryVersion = '2.10'
    googleMapUtilsVerion = '2.3.0'
    androidxLegacyVersion = '1.0.0'
    materialVersion = '1.12.0'
    //Unit test libraries.
    junitVersion = '4.13.2'
    junitExtVersion = '1.2.1'
    mockitoVersion = '5.13.0'
    espressoVersion = '3.6.1'
    //Rx java libraries
    rxjavaVersion = '2.2.21'
    rxbindingVersion = '2.2.0'
    rxlifecycleVersion = '2.2.2'
    rxpermissionsVersion = '0.9.5'
    // Other libraries
    visionVersion = '20.1.3'
    daggerVersion = '2.52'
    retrofitVersion = '2.9.0'
    qualtricsVersion = '2.26.0'
    moshiVersion = '1.13.0'
    mpAndroidChart = 'v3.1.0'
    jodaTimeVersion = '2.10.12.2'
    okHttpLoggingVersion = '5.0.0-alpha.5'
    autofitTextViewVersion = '0.2.+'
    stickyHeaderRecyclerViewVersion = '0.7.10'
    arcLayoutVersion = '1.1.0'
    picassoVersion = '2.71828'
    uuIDgeneratorVersion = '4.0.1'
    appCompatVersion = '1.4.1'
    //Apache commons library.
    commonsApachaeTextVersion = '1.9'
    commonsCodecVersion = '1.15'
    gsonVersion = '2.9.0'
    eventbusVersion = "3.3.1"
    lottieversion = '5.0.2'
    branchVersion = '5.1.1'
    installReferrerVersion = "2.2"
    flexboxVersion = "2.0.1"
    androidxVersion = '1.5.1'
    recyclerViewVersion = '1.3.2'
    startRunTimeVersion = '1.1.1'
    cardViewVersion = "1.0.0"
    wrappingViewPagerVersion = '1.0.1'
    firebaseAnalyticsVersion = '20.1.0'
    firbaseCrashlyticsVersion = '18.2.8'
    appsFlyerVersion = '6.14.2'
    nanoxlsVersion = '1.2.8'
    cameraVersion = '1.3.0'
    tapAndPayVersion = '18.3.2'
    legacySupportVersion = '1.0.0'
    glideVersion = '4.16.0'
    pdfboxAndroidVersion = '2.0.27.0'
    mockkVersion = '1.13.3'
    navigationUiKtx = '2.5.3'
    livedataKtx = '2.4.1'
    viewmodelKtx = '2.4.1'
    navigationFragmentKtx = '2.5.3'
    coreKtxVersion = '1.12.1'
    //We should not update this io-socket library, As this is causing issue
    iosocketVersion = "1.0.0"
    // We should not update the libs apacheCommonsIO and commonsLangVersion as
    // updating this start causing crash in device below 8
    apacheCommonsIO = '2.5'
    commonsLangVersion = '3.9'
    // React Native version for POC
    reactNativeVersion = '0.72.6'
}
subprojects {
    tasks.withType(Test).configureEach {
        jvmArgs = jvmArgs + ['--add-opens=java.base/java.lang=ALL-UNNAMED']
    }
}