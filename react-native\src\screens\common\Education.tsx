import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useRoute } from '@react-navigation/native';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '../../navigation/AppNavigator';

import Card from '../../components/common/Card';
import Button from '../../components/common/Button';

type RouteProp = RouteProp<RootStackParamList, 'Education'>;

interface EducationSection {
  id: string;
  title: string;
  content: string;
  icon: string;
  expanded: boolean;
}

const Education: React.FC = () => {
  const route = useRoute<RouteProp>();
  const { productType = 'Insurance', title = 'Learn more' } = route.params || {};

  const [sections, setSections] = useState<EducationSection[]>([
    {
      id: '1',
      title: 'What is funeral insurance?',
      content: 'Funeral insurance provides financial protection to help cover the costs of a funeral when a loved one passes away. It ensures that your family won\'t face financial hardship during an already difficult time.',
      icon: '💰',
      expanded: false,
    },
    {
      id: '2',
      title: 'How does it work?',
      content: 'You pay a monthly premium, and when a covered person passes away, the insurance pays out a lump sum to help cover funeral expenses. The payout can be used for burial costs, catering, transport, and other related expenses.',
      icon: '⚙️',
      expanded: false,
    },
    {
      id: '3',
      title: 'Who can be covered?',
      content: 'You can cover yourself, your spouse, children, parents, and other family members. Each person covered will have their own premium based on their age and the cover amount selected.',
      icon: '👨‍👩‍👧‍👦',
      expanded: false,
    },
    {
      id: '4',
      title: 'Waiting periods',
      content: 'There is no waiting period for accidental death. For natural death, there is a 6-month waiting period. This means claims for natural death can only be made after 6 months from the policy start date.',
      icon: '⏰',
      expanded: false,
    },
    {
      id: '5',
      title: 'Making a claim',
      content: 'To make a claim, contact our claims department on 0860 555 111. You\'ll need to provide the death certificate and other required documents. Claims are typically processed within 48 hours.',
      icon: '📋',
      expanded: false,
    },
    {
      id: '6',
      title: 'Premium payments',
      content: 'Premiums are collected monthly via debit order from your bank account. You can choose to pay on the 1st, 15th, or 25th of each month. Ensure sufficient funds are available to avoid policy lapses.',
      icon: '💳',
      expanded: false,
    },
  ]);

  const toggleSection = (sectionId: string) => {
    setSections(prev => 
      prev.map(section => 
        section.id === sectionId 
          ? { ...section, expanded: !section.expanded }
          : section
      )
    );
  };

  const handleGetQuote = () => {
    Alert.alert(
      'React Native POC',
      'This would start the insurance quote process. Demonstrates React Native\'s ability to handle complex user journeys from educational content.',
      [{ text: 'OK' }]
    );
  };

  const handleContactUs = () => {
    Alert.alert(
      'React Native POC',
      'This would open contact options (phone, email, chat). Demonstrates React Native\'s device integration capabilities.',
      [{ text: 'OK' }]
    );
  };

  const renderSection = (section: EducationSection) => (
    <Card
      key={section.id}
      style={styles.sectionCard}
      onPress={() => toggleSection(section.id)}
      variant="default"
      padding="medium"
      margin="small"
    >
      <View style={styles.sectionHeader}>
        <View style={styles.sectionTitleContainer}>
          <Text style={styles.sectionIcon}>{section.icon}</Text>
          <Text style={styles.sectionTitle}>{section.title}</Text>
        </View>
        <Text style={styles.expandIcon}>
          {section.expanded ? '−' : '+'}
        </Text>
      </View>
      
      {section.expanded && (
        <View style={styles.sectionContent}>
          <Text style={styles.sectionText}>{section.content}</Text>
        </View>
      )}
    </Card>
  );

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.subtitle}>
            Learn about {productType.toLowerCase()} insurance and how it can protect you and your family.
          </Text>
        </View>

        {/* Hero Section */}
        <Card style={styles.heroCard} variant="elevated" padding="large" margin="medium">
          <View style={styles.heroContent}>
            <Text style={styles.heroIcon}>🛡️</Text>
            <Text style={styles.heroTitle}>Protect what matters most</Text>
            <Text style={styles.heroText}>
              Insurance provides peace of mind by protecting you and your loved ones from unexpected financial burdens.
            </Text>
          </View>
        </Card>

        {/* Quick Facts */}
        <Card style={styles.factsCard} variant="outlined" padding="medium" margin="medium">
          <Text style={styles.factsTitle}>Quick Facts</Text>
          <View style={styles.factsList}>
            <View style={styles.factItem}>
              <Text style={styles.factBullet}>✓</Text>
              <Text style={styles.factText}>Cover from R25 per month</Text>
            </View>
            <View style={styles.factItem}>
              <Text style={styles.factBullet}>✓</Text>
              <Text style={styles.factText}>No medical examinations required</Text>
            </View>
            <View style={styles.factItem}>
              <Text style={styles.factBullet}>✓</Text>
              <Text style={styles.factText}>Claims paid within 48 hours</Text>
            </View>
            <View style={styles.factItem}>
              <Text style={styles.factBullet}>✓</Text>
              <Text style={styles.factText}>24/7 customer support</Text>
            </View>
          </View>
        </Card>

        {/* Educational Sections */}
        <View style={styles.sectionsContainer}>
          <Text style={styles.sectionsTitle}>Frequently Asked Questions</Text>
          {sections.map(renderSection)}
        </View>

        {/* Call to Action */}
        <Card style={styles.ctaCard} variant="elevated" padding="large" margin="medium">
          <Text style={styles.ctaTitle}>Ready to get covered?</Text>
          <Text style={styles.ctaText}>
            Get a personalized quote in just a few minutes and start protecting your family today.
          </Text>
          <View style={styles.ctaButtons}>
            <Button
              title="Get a Quote"
              onPress={handleGetQuote}
              variant="primary"
              size="large"
              style={styles.ctaButton}
            />
            <Button
              title="Contact Us"
              onPress={handleContactUs}
              variant="outline"
              size="large"
              style={styles.ctaButton}
            />
          </View>
        </Card>

        {/* React Native POC Notice */}
        <Card style={styles.pocNotice} variant="flat" padding="medium" margin="medium">
          <View style={styles.pocContent}>
            <Text style={styles.pocIcon}>📚</Text>
            <Text style={styles.pocText}>
              React Native POC - Rich educational content with interactive elements and smooth animations
            </Text>
          </View>
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    lineHeight: 22,
  },
  heroCard: {
    backgroundColor: '#f8fff8',
    borderColor: '#00A651',
    borderWidth: 1,
  },
  heroContent: {
    alignItems: 'center',
    textAlign: 'center',
  },
  heroIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  heroTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
    textAlign: 'center',
  },
  heroText: {
    fontSize: 16,
    color: '#666666',
    lineHeight: 22,
    textAlign: 'center',
  },
  factsCard: {
    backgroundColor: '#fff9e6',
    borderColor: '#ffc107',
  },
  factsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  factsList: {
    gap: 8,
  },
  factItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  factBullet: {
    fontSize: 16,
    color: '#00A651',
    marginRight: 12,
    fontWeight: '600',
  },
  factText: {
    fontSize: 14,
    color: '#333333',
    flex: 1,
  },
  sectionsContainer: {
    paddingHorizontal: 10,
    marginTop: 20,
  },
  sectionsTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginLeft: 10,
    marginBottom: 16,
  },
  sectionCard: {
    marginHorizontal: 10,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sectionIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    flex: 1,
  },
  expandIcon: {
    fontSize: 20,
    color: '#666666',
    fontWeight: '300',
  },
  sectionContent: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  sectionText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  ctaCard: {
    backgroundColor: '#f0f8ff',
    borderColor: '#007bff',
    borderWidth: 1,
    alignItems: 'center',
  },
  ctaTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
    textAlign: 'center',
  },
  ctaText: {
    fontSize: 16,
    color: '#666666',
    lineHeight: 22,
    textAlign: 'center',
    marginBottom: 20,
  },
  ctaButtons: {
    flexDirection: 'row',
    gap: 12,
    width: '100%',
  },
  ctaButton: {
    flex: 1,
  },
  pocNotice: {
    backgroundColor: '#FFF3E0',
    borderRadius: 8,
  },
  pocContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pocIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  pocText: {
    fontSize: 14,
    color: '#FF6B35',
    flex: 1,
    lineHeight: 20,
  },
});

export default Education;
