package za.co.nedbank.services.insurance.view.vvap_policy_admin.banking

import io.reactivex.Observable
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.Mockito.*
import org.mockito.junit.MockitoJUnitRunner
import za.co.nedbank.core.domain.CachableValue
import za.co.nedbank.core.domain.model.banklist.BankDataModel
import za.co.nedbank.core.errors.Error
import za.co.nedbank.core.errors.ErrorHandler
import za.co.nedbank.core.navigation.NavigationResult
import za.co.nedbank.core.navigation.NavigationRouter
import za.co.nedbank.core.navigation.NavigationTarget
import za.co.nedbank.core.tracking.Analytics
import za.co.nedbank.core.tracking.InsuranceTrackingEvent
import za.co.nedbank.core.tracking.TrackingEvent
import za.co.nedbank.core.tracking.adobe.AdobeContextData
import za.co.nedbank.core.validation.NonEmptyTextValidator
import za.co.nedbank.core.validation.ValidationResult
import za.co.nedbank.core.view.banklist.model.BankBranchViewModel
import za.co.nedbank.core.view.banklist.model.BankViewModel
import za.co.nedbank.core.view.mapper.BankDataToViewModelMapper
import za.co.nedbank.services.Constants
import za.co.nedbank.services.domain.model.overview.AccountsOverview
import za.co.nedbank.services.domain.model.overview.Overview
import za.co.nedbank.services.domain.model.overview.OverviewType
import za.co.nedbank.services.domain.usecase.debit.BankListUseCase
import za.co.nedbank.services.domain.usecase.overview.GetOverviewUseCase
import za.co.nedbank.services.insurance.domain.data.request.funeral.quote.build_own_package.issue_policy.CDVRequestDataModel
import za.co.nedbank.services.insurance.domain.data.request.vvaps.manage_policy.finance.submit_finance.FinanceChangesRequestDataModel
import za.co.nedbank.services.insurance.domain.data.response.generic.account_verification.CDVResponseDataModel
import za.co.nedbank.services.insurance.domain.data.response.vvap.manage_policy.finance.fetch_premium.FinancePremiumResponseDataModel
import za.co.nedbank.services.insurance.domain.data.response.vvap.manage_policy.finance.submit_finance.FinanceChangesResponseDataModel
import za.co.nedbank.services.insurance.domain.usecase.common.CDVRequestUseCase
import za.co.nedbank.services.insurance.domain.usecase.common.GetAccountTypeUseCase
import za.co.nedbank.services.insurance.domain.usecase.personal_lines.manage_policy.banking.GetPLPremiumForBankingChangeUseCase
import za.co.nedbank.services.insurance.domain.usecase.vvap.manage_policy.banking.UpdateBankingDetailsUseCase
import za.co.nedbank.services.insurance.view.generic.common.other.model.InsuranceCodeDescriptionViewModel
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants
import za.co.nedbank.services.insurance.view.other.listeners.generic.ISelectedViewModel
import za.co.nedbank.services.insurance.view.other.mapper.generic.account_verification.CDVRequestViewToDataMapper
import za.co.nedbank.services.insurance.view.other.mapper.generic.account_verification.CDVResponseDataToViewMapper
import za.co.nedbank.services.insurance.view.other.mapper.vvaps.manage_policy.finance.FinanceChangesRequestViewToDataMapper
import za.co.nedbank.services.insurance.view.other.mapper.vvaps.manage_policy.finance.FinanceChangesResponseDataToViewMapper
import za.co.nedbank.services.insurance.view.other.mapper.vvaps.manage_policy.finance.FinancePremiumResponseDataToViewMapper
import za.co.nedbank.services.insurance.view.other.model.request.generic.account_verification.CDVRequestViewModel
import za.co.nedbank.services.insurance.view.other.model.request.vvaps.manage_policy.finance.submit_finance.FinanceChangesRequestViewModel
import za.co.nedbank.services.insurance.view.other.model.response.generic.account_verification.CDVResponseViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.BankAccountViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.submit_finance.FinanceChangesResponseViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.submit_finance.ResultSetViewModel
import za.co.nedbank.services.insurance.view.vvap.policy_admin.banking.edit_banking_detail.PolicyAdminEditBankingDetailsPresenter
import za.co.nedbank.services.insurance.view.vvap.policy_admin.banking.edit_banking_detail.PolicyAdminEditBankingDetailsView
import za.co.nedbank.services.insurance.view.vvap.policy_admin.other.helper.EditBankingDetailsHelper
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget
import za.co.nedbank.uisdk.validation.ValidatableInput
import za.co.nedbank.core.view.model.AccountViewModel as CoreAccountViewModel
import za.co.nedbank.services.domain.model.overview.Overview as DomainOverview
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.BankAccountViewModel as ClaimBankAccountViewModel


@RunWith(MockitoJUnitRunner.Silent::class)
class PolicyAdminEditBankingDetailsPresenterTest {
    @Mock
    private val mNavigationRouter: NavigationRouter? = null

    @Mock
    private val mNonEmptyTextValidator: NonEmptyTextValidator? = null

    @Mock
    private val mGetAccountTypeList: GetAccountTypeUseCase? = null

    @Mock
    private val mCDVRequestUseCase: CDVRequestUseCase? = null

    @Mock
    private val mCDVViewToDataMapper: CDVRequestViewToDataMapper? = null

    @Mock
    private val getPLPremiumForBankingChangeUseCase: GetPLPremiumForBankingChangeUseCase? = null

    @Mock
    private val mCDVDataToViewMapper: CDVResponseDataToViewMapper? = null

    @Mock
    private val mGetOverviewUseCase: GetOverviewUseCase? = null

    @Mock
    private val updateBankingDetailsUseCase: UpdateBankingDetailsUseCase? = null

    @Mock
    private val financeChangesRequestViewToDataMapper: FinanceChangesRequestViewToDataMapper? = null

    @Mock
    private val financePremiumResponseDataToViewMapper: FinancePremiumResponseDataToViewMapper? =
        null

    @Mock
    private val financeChangesResponseViewToDataMapper: FinanceChangesResponseDataToViewMapper? =
        null

    @Mock
    private val mBankListUseCase: BankListUseCase? = null

    @Mock
    private val mBankDataToViewModel: BankDataToViewModelMapper? = null

    @Mock
    private val iSelectedViewModel: List<ISelectedViewModel>? = null

    @Mock
    private val cachableValue: CachableValue<Overview>? = null

    @Mock
    private val listBankDataModel: List<BankDataModel>? = null

    @Mock
    private val mBankAccountViewModel: BankAccountViewModel? = null

    @Mock
    private val mHelper: EditBankingDetailsHelper? = null

    @Mock
    private val mFinanceChangesRequestViewModel: FinanceChangesRequestViewModel? = null

    @Mock
    private val mFinanceChangesRequestDataModel: FinanceChangesRequestDataModel? = null


    @Mock
    private val mFinanceChangesResponseViewModel: FinanceChangesResponseViewModel? = null

    @Mock
    private val mFinanceChangesResponseDataModel: FinanceChangesResponseDataModel? = null

    @Mock
    private val mAccountDetailData: PolicyAdminEditBankingDetailsPresenter.AccountDetailsData? =
        null

    @Mock
    private val mCDVRequestViewModel: CDVRequestViewModel? = null

    @Mock
    private val mCDVRequestDataModel: CDVRequestDataModel? = null

    @Mock
    private val mCDVResponseDataModel: CDVResponseDataModel? = null

    @Mock
    private val mCDVResponseViewModel: CDVResponseViewModel? = null

    @Mock
    private val requestDataModelList: ArrayList<CDVRequestDataModel>? = null

    @Mock
    private val responseDataModelList: ArrayList<CDVResponseDataModel>? = null

    @Mock
    private val responseViewModelList: ArrayList<CDVResponseViewModel>? = null

    @Mock
    private val bankNameValidatableInput: ValidatableInput<String>? = null

    @Mock
    private val branchCodeValidatableInput: ValidatableInput<String>? = null

    @Mock
    private val accountTypeValidatableInput: ValidatableInput<String>? = null

    @Mock
    private val accountNoValidatableInput: ValidatableInput<String>? = null

    @Mock
    private val mSuccessResult: ValidationResult? = null

    @Mock
    private val mFailureResult: ValidationResult? = null

    @Mock
    private val mEditBankingDetailsHelper: EditBankingDetailsHelper? = null

    @Mock
    private val mErrorHandler: ErrorHandler? = null

    @Mock
    private val mThrowable: Throwable? = null

    @Mock
    private val mError: Error? = null

    @Mock
    var mView: PolicyAdminEditBankingDetailsView? = null

    @Mock
    var mAnalytics: Analytics? = null

    @InjectMocks
    private val mPresenter: PolicyAdminEditBankingDetailsPresenter? = null

    private var mSpy: PolicyAdminEditBankingDetailsPresenter? = null

    @Before
    @Throws(Exception::class)
    fun setUp() {
        mPresenter!!.bind(mView)
        mSpy = Mockito.spy(mPresenter)
        mFailureResult?.addError("error")
    }

    @Test
    fun fetchNedBankAccountAndTypeTest() {
        Mockito.`when`(mGetAccountTypeList!!.execute())
            .thenReturn(Observable.just(iSelectedViewModel))
        Mockito.`when`(mGetOverviewUseCase!!.execute()).thenReturn(Observable.just(cachableValue))
        Mockito.`when`(mBankListUseCase!!.execute()).thenReturn(Observable.just(listBankDataModel))

        mPresenter!!.fetchNedbankAccountAndType()
        Mockito.verify(mView)!!.setAccountTypeList(Mockito.any())
    }

    @Test
    fun fetchNedBankAccountAndTypeFailureTest() {
        `when`(mErrorHandler?.getErrorMessage(mThrowable)).thenReturn(mError)
        `when`(
            mGetAccountTypeList!!.execute()
        ).thenReturn(Observable.error(mThrowable))
        `when`(
            mGetOverviewUseCase!!.execute()
        ).thenReturn(Observable.error(mThrowable))
        `when`(
            mBankListUseCase!!.execute()
        ).thenReturn(Observable.error(mThrowable))

        mPresenter?.fetchNedbankAccountAndType()
        verify(mView, atLeastOnce())!!.showProgressBar(false)
        verify(mView)!!.showAPIError()
    }

//    @Test
//    fun updateBankingDetailsTest() {
//        mockBankingResponseCode("R00")
//
//        mPresenter!!.updateBankingDetails(mHelper!!, "123456", "123456")
//        Mockito.verify(mView)!!.submitSuccess(mHelper)
//
//        mockBankingResponseCode("R01")
//
//        mPresenter.updateBankingDetails(mHelper, "123456", "123456")
//        Mockito.verify(mView)!!.goToBankingScreenWithError()
//    }

    private fun mockBankingResponseCode(resultCode: String) {
        val resultSetViewModel = ResultSetViewModel()
        resultSetViewModel.resultCode = resultCode

        `when`(mFinanceChangesResponseViewModel?.resultSet).thenReturn(resultSetViewModel)
        `when`(financeChangesResponseViewToDataMapper?.map(mFinanceChangesResponseDataModel!!)).thenReturn(
            mFinanceChangesResponseViewModel
        )
        `when`(updateBankingDetailsUseCase!!.execute(any())).thenReturn(
            Observable.just(
                mFinanceChangesResponseDataModel
            )
        )
    }

//    @Test
//    fun updateBankingDetailsFailTest() {
//        val resultSetViewModel = ResultSetViewModel()
//        resultSetViewModel.resultCode = "R02"
//
//        `when`(mFinanceChangesResponseViewModel?.resultSet).thenReturn(resultSetViewModel)
//        `when`(financeChangesResponseViewToDataMapper?.map(mFinanceChangesResponseDataModel!!)).thenReturn(mFinanceChangesResponseViewModel)
//        `when`(updateBankingDetailsUseCase!!.execute(any())).thenReturn(Observable.just(mFinanceChangesResponseDataModel))
//
//        mPresenter!!.updateBankingDetails(mHelper!!, "123456", "123456")
//        verify(mView)!!.showSubmitError(mHelper)
//    }
//
//    @Test
//    fun updateBankingDetailsFailureTest() {
//        `when`(mErrorHandler?.getErrorMessage(mThrowable)).thenReturn(mError)
//        `when`(
//            updateBankingDetailsUseCase!!.execute(Mockito.any())
//        ).thenReturn(Observable.error(mThrowable))
//
//        mPresenter!!.updateBankingDetails(mHelper!!, "123456", "123456")
//        Mockito.verify(mView)!!.showSubmitError(mHelper)
//    }

    @Test
    fun showSubmitAPIErrorTest() {
        mPresenter?.showSubmitAPIError(mHelper!!)
        Mockito.verify(mView!!).showSubmitError(mHelper!!)
    }

    @Test
    fun showSubmitAPIErrorNullViewTest() {
        mPresenter!!.unbind()
        mPresenter.showSubmitAPIError(mHelper!!)
        Mockito.verifyNoMoreInteractions(mView)
    }

    @Test
    fun showAPIErrorTest() {
        mPresenter?.showAPIError()
        Mockito.verify(mView!!).showAPIError()
        Mockito.verify(mView!!).showProgressBar(false)
    }

    @Test
    fun verifyAccountNoTest() {

        val cDVResponseViewModel = CDVResponseViewModel()
        cDVResponseViewModel.accountType = "123"
        cDVResponseViewModel.passedY = "Y"
        val listDVResponseViewModel = mutableListOf<CDVResponseViewModel>()
        listDVResponseViewModel.add(cDVResponseViewModel)

        `when`(
            mCDVViewToDataMapper!!.mapRequestViewToDataModel(
                ArgumentMatchers.anyList()
            )
        ).thenReturn(requestDataModelList)
        Mockito.doReturn(listDVResponseViewModel).`when`(mCDVDataToViewMapper)!!
            .mapResponseDataToViewModel(responseDataModelList)
        Mockito.doReturn(Observable.just(responseDataModelList)).`when`(mCDVRequestUseCase)!!
            .execute(requestDataModelList)
        mPresenter!!.verifyAccountNo()
        Mockito.verify(mView)!!.moveToNext()
    }

    @Test
    fun verifyAccountNoErrorTest() {

        val cDVResponseViewModel = CDVResponseViewModel()
        cDVResponseViewModel.accountType = "123"
        cDVResponseViewModel.passedY = "N"
        val listDVResponseViewModel = mutableListOf<CDVResponseViewModel>()
        listDVResponseViewModel.add(cDVResponseViewModel)

        `when`(
            mCDVViewToDataMapper!!.mapRequestViewToDataModel(
                ArgumentMatchers.anyList()
            )
        ).thenReturn(requestDataModelList)
        Mockito.doReturn(listDVResponseViewModel).`when`(mCDVDataToViewMapper)!!
            .mapResponseDataToViewModel(responseDataModelList)
        Mockito.doReturn(Observable.just(responseDataModelList)).`when`(mCDVRequestUseCase)!!
            .execute(requestDataModelList)
        mPresenter!!.verifyAccountNo()
        Mockito.verify(mView)!!.showAccountAPIError()
    }

    @Test
    fun verifyAccountNoFailureTest() {
        `when`(mErrorHandler?.getErrorMessage(mThrowable)).thenReturn(mError)
        `when`(
            mCDVRequestUseCase!!.execute(any())
        ).thenReturn(Observable.error(mThrowable))

        mPresenter?.verifyAccountNo()
        verify(mView, atLeastOnce())!!.showProgressBar(false)
        verify(mView)!!.showAccountAPIError()
    }

    @Test
    fun getRequestViewModelTest() {
        Assert.assertNotNull(mPresenter!!.getRequestViewModel())
    }

    @Test
    fun navigateToSuccessTest() {
        Mockito.`when`(mNavigationRouter!!.navigateTo(ArgumentMatchers.any()))
            .thenReturn(true)
        mPresenter!!.navigateToSuccess(mHelper!!, "VVAPS")
        Mockito.verify(mNavigationRouter)
            .navigateTo(NavigationTarget.to(ServicesNavigationTarget.INSURANCE_VVAPS_ADMIN_POLICY_SUCCESS))
    }

    @Test
    fun navigateToBankingDetailTest() {
        Mockito.`when`(mNavigationRouter!!.navigateTo(ArgumentMatchers.any()))
            .thenReturn(true)
        mPresenter!!.navigateToBankingDetail()
        Mockito.verify(mNavigationRouter)
            .navigateTo(NavigationTarget.to(ServicesNavigationTarget.INSURANCE_VVAPS_BANKING_DETAILS))
    }

    @Test
    fun handleAccountTypeClickTest() {
        val model =
            InsuranceCodeDescriptionViewModel()
        val params: MutableMap<String, Any> = HashMap()
        params[InsuranceConstants.ParamKeys.PARAM_SELECTED_TYPE_MODEL] = model
        val navigationResult = NavigationResult(true, params)
        Mockito.`when`(mNavigationRouter!!.navigateWithResult(ArgumentMatchers.any()))
            .thenReturn(Observable.just(navigationResult))
        mPresenter!!.handleAccountTypeClick()
        Mockito.verify(mNavigationRouter)
            .navigateWithResult(NavigationTarget.to(ServicesNavigationTarget.INSURANCE_COMMON_SELECTION_SCREEN))
        verify(mView)!!.setAccountTypeData(model)
    }


    @Test
    fun handleAccountTypeClickFailureTest() {
        `when`(mView?.getAccountTypeList()).thenReturn(ArrayList<ISelectedViewModel>())
        val params: MutableMap<String, Any> = HashMap()
        var navigationResult = NavigationResult(false, params)
        `when`(mNavigationRouter!!.navigateWithResult(ArgumentMatchers.any()))
            .thenReturn(Observable.just(navigationResult))
        mPresenter!!.handleAccountTypeClick()
        Mockito.verify(mNavigationRouter)
            .navigateWithResult(NavigationTarget.to(ServicesNavigationTarget.INSURANCE_COMMON_SELECTION_SCREEN))
    }

    @Test
    fun navigateToBranchListScreenTest() {
        val branchViewModel = BankBranchViewModel()
        branchViewModel.branchCode = "abc"
        branchViewModel.branchName = "abc"
        val params: MutableMap<String, Any> = HashMap()
        params[InsuranceConstants.BundleKeys.SELECTED_BRANCH_MODEL] = branchViewModel
        val navigationResult = NavigationResult(true, params)
        Mockito.`when`(mNavigationRouter!!.navigateWithResult(ArgumentMatchers.any()))
            .thenReturn(Observable.just(navigationResult))
        mPresenter!!.navigateToBranchListScreen(ArrayList<BankBranchViewModel>())
        Mockito.verify(mNavigationRouter)
            .navigateWithResult(NavigationTarget.to(ServicesNavigationTarget.INSURANCE_BRANCH_LIST_ACTIVITY))
        Mockito.verify(mView)!!.setSelectedBranch(branchViewModel)
    }

    @Test
    fun navigateToBranchListScreenFailureTest() {
        val params: MutableMap<String, Any> = HashMap()
        var navigationResult = NavigationResult(false, params)
        `when`(mNavigationRouter!!.navigateWithResult(ArgumentMatchers.any()))
            .thenReturn(Observable.just(navigationResult))
        mPresenter!!.navigateToBranchListScreen(ArrayList<BankBranchViewModel>())
        Mockito.verify(mNavigationRouter)
            .navigateWithResult(NavigationTarget.to(ServicesNavigationTarget.INSURANCE_BRANCH_LIST_ACTIVITY))
        verifyNoMoreInteractions(mView)

        mPresenter.unbind()
        navigationResult = NavigationResult(true, params)
        `when`(mNavigationRouter!!.navigateWithResult(ArgumentMatchers.any()))
            .thenReturn(Observable.just(navigationResult))
        mPresenter!!.navigateToBranchListScreen(ArrayList<BankBranchViewModel>())
        verifyNoMoreInteractions(mView)
    }


    @Test
    fun navigateToBankingListScreenTest() {
        val bankViewModel = BankViewModel()
        bankViewModel.bankCode = "abc"
        bankViewModel.bankName = "abc"
        val params: MutableMap<String, Any> = HashMap()
        params[InsuranceConstants.BundleKeys.SELECTED_BANK] = bankViewModel
        val navigationResult = NavigationResult(true, params)
        `when`(mNavigationRouter!!.navigateWithResult(ArgumentMatchers.any()))
            .thenReturn(Observable.just(navigationResult))
        mPresenter!!.navigateToBankingListScreen()
        verify(mNavigationRouter)
            .navigateWithResult(NavigationTarget.to(ServicesNavigationTarget.INSURANCE_BANKLIST_SCREEN))
        verify(mView)!!.setSelectedBank(bankViewModel)
    }

    @Test
    fun navigateToBankingListScreenFailureTest() {
        val params: MutableMap<String, Any> = HashMap()
        var navigationResult = NavigationResult(false, params)
        `when`(mNavigationRouter!!.navigateWithResult(ArgumentMatchers.any()))
            .thenReturn(Observable.just(navigationResult))
        mPresenter!!.navigateToBankingListScreen()
        verify(mNavigationRouter)
            .navigateWithResult(NavigationTarget.to(ServicesNavigationTarget.INSURANCE_BANKLIST_SCREEN))
        verifyNoMoreInteractions(mView)

        mPresenter.unbind()
        navigationResult = NavigationResult(true, params)
        `when`(mNavigationRouter!!.navigateWithResult(ArgumentMatchers.any()))
            .thenReturn(Observable.just(navigationResult))
        mPresenter!!.navigateToBankingListScreen()
        verifyNoMoreInteractions(mView)
    }

    @Test
    fun areBankNamesDifferentTest() {
        Assert.assertTrue(mPresenter!!.areBankNamesDifferent("abc", "xyz"))
    }

    @Test
    fun areBankNamesDifferentTestFail() {
        Assert.assertFalse(mPresenter!!.areBankNamesDifferent("abc", "abc"))
        Assert.assertFalse(mPresenter!!.areBankNamesDifferent("", "xyz"))
        Assert.assertFalse(mPresenter!!.areBankNamesDifferent("abc", ""))
    }

    @Test
    fun validateAllFieldsTest() {
        `when`(mSuccessResult!!.isOk).thenReturn(true)
        `when`(mNonEmptyTextValidator!!.validateInput(ArgumentMatchers.any()))
            .thenReturn(mSuccessResult)
        `when`(bankNameValidatableInput!!.value).thenReturn("abc")
        `when`(branchCodeValidatableInput!!.value).thenReturn("cde")
        `when`(accountTypeValidatableInput!!.value).thenReturn("fgh")
        `when`(accountNoValidatableInput!!.value).thenReturn("*********")

        mPresenter!!.validateAllFields(
            bankNameValidatableInput!!,
            branchCodeValidatableInput!!,
            accountTypeValidatableInput!!,
            accountNoValidatableInput!!,
            true,
            true
        )
        Mockito.verify(mView)!!.setNextButtonEnable(true)
    }

    @Test
    fun validateAllFieldsAccountErrorTest() {
        `when`(mSuccessResult!!.isOk).thenReturn(true)
        `when`(mNonEmptyTextValidator!!.validateInput(ArgumentMatchers.any()))
            .thenReturn(mSuccessResult)
        `when`(bankNameValidatableInput!!.value).thenReturn("abc")
        `when`(branchCodeValidatableInput!!.value).thenReturn("cde")
        `when`(accountTypeValidatableInput!!.value).thenReturn("fgh")
        `when`(accountNoValidatableInput!!.value).thenReturn("123456")

        mPresenter!!.validateAllFields(
            bankNameValidatableInput!!,
            branchCodeValidatableInput!!,
            accountTypeValidatableInput!!,
            accountNoValidatableInput!!,
            true,
            true
        )
        Mockito.verify(mView)!!.showAccountTextError()
        Mockito.verify(mView)!!.setNextButtonEnable(false)
    }

    /** WARNING : DON'T CHANGE CONTENT OF THIS METHOD*/
    @Test
    fun validateAllFieldsFailureTest() {
        val data = "abc"
        val accountNo = "*********"
        val emptyData = ""
        `when`(mSuccessResult!!.isOk).thenReturn(true)
        `when`(mNonEmptyTextValidator!!.validateInput(data)).thenReturn(mSuccessResult)
        `when`(mNonEmptyTextValidator!!.validateInput(accountNo)).thenReturn(mSuccessResult)
        `when`(mNonEmptyTextValidator!!.validateInput(emptyData)).thenReturn(mFailureResult)
        `when`(bankNameValidatableInput!!.value).thenReturn(emptyData)
        `when`(branchCodeValidatableInput!!.value).thenReturn(data)
        `when`(accountTypeValidatableInput!!.value).thenReturn(data)
        `when`(accountNoValidatableInput!!.value).thenReturn(accountNo)

        mPresenter!!.validateAllFields(
            bankNameValidatableInput!!,
            branchCodeValidatableInput!!,
            accountTypeValidatableInput!!,
            accountNoValidatableInput!!,
            true,
            true
        )

        `when`(bankNameValidatableInput!!.value).thenReturn(data)
        `when`(branchCodeValidatableInput!!.value).thenReturn(emptyData)
        mPresenter!!.validateAllFields(
            bankNameValidatableInput!!,
            branchCodeValidatableInput!!,
            accountTypeValidatableInput!!,
            accountNoValidatableInput!!,
            true,
            true
        )

        `when`(branchCodeValidatableInput!!.value).thenReturn(data)
        `when`(accountTypeValidatableInput!!.value).thenReturn(emptyData)
        mPresenter!!.validateAllFields(
            bankNameValidatableInput!!,
            branchCodeValidatableInput!!,
            accountTypeValidatableInput!!,
            accountNoValidatableInput!!,
            true,
            true
        )

        `when`(accountTypeValidatableInput!!.value).thenReturn(data)
        `when`(accountNoValidatableInput!!.value).thenReturn(emptyData)
        mPresenter!!.validateAllFields(
            bankNameValidatableInput!!,
            branchCodeValidatableInput!!,
            accountTypeValidatableInput!!,
            accountNoValidatableInput!!,
            true,
            true
        )

        `when`(accountNoValidatableInput!!.value).thenReturn(accountNo)
        mPresenter!!.validateAllFields(
            bankNameValidatableInput!!,
            branchCodeValidatableInput!!,
            accountTypeValidatableInput!!,
            accountNoValidatableInput!!,
            false,
            true
        )

        mPresenter!!.validateAllFields(
            bankNameValidatableInput!!,
            branchCodeValidatableInput!!,
            accountTypeValidatableInput!!,
            accountNoValidatableInput!!,
            true,
            false
        )


        Mockito.verify(mView, times(6))!!.setNextButtonEnable(false)
    }

//    @Test
//     fun editFinanceDetailsTest(){
//        Mockito.`when`(mNavigationRouter!!.navigateTo(ArgumentMatchers.any()))
//            .thenReturn(true)
//        mPresenter!!.editFinanceDetails(AccountViewModel(), mEditFinanceDetailsHelper!!)
//        Mockito.verify(mNavigationRouter)
//            .navigateTo(NavigationTarget.to(ServicesNavigationTarget.INSURANCE_VVAPS_EDIT_FINANCE_DETAILS))
//     }

    @Test
    fun moveToProductListScreenTest() {
        `when`(mNavigationRouter!!.navigateTo(ArgumentMatchers.any()))
            .thenReturn(true)
        mPresenter!!.moveToManagePolicyScreen()
        Mockito.verify(mNavigationRouter)
            .navigateTo(NavigationTarget.to(ServicesNavigationTarget.INSURANCE_DETAILS_CONTAINER))
        Mockito.verify(mView)?.close()
    }

    @Test
    fun getAlreadySelectedAccountTypeTest() {
        val list = ArrayList<ISelectedViewModel>()
        val model =
            InsuranceCodeDescriptionViewModel()
        model.code = "abc"
        model.description = "abc"
        list.add(model)
        `when`(mView?.getAccountTypeList()).thenReturn(list)
        Assert.assertNotNull(mPresenter?.getAlreadySelectedAccountType("abc"))
    }

    @Test
    fun sendEventWithProductForManagePolicyGAP() {
        val cdata = java.util.HashMap<String?, Any?>()
        val adobeContextData = AdobeContextData(cdata)
        adobeContextData.setCategoryAndProduct(
            TrackingEvent.ANALYTICS.INSURANCE_PRODUCT_CATEGORY,
            TrackingEvent.ANALYTICS.VVAPS_TITLE
        )
        adobeContextData.setSubProduct(InsuranceConstants.AnalyticsConstants.SUBPRODUCT_GAP)
        adobeContextData.setProductAccount(TrackingEvent.ANALYTICS.VVAPS_TITLE)
        adobeContextData.setProductCategory(TrackingEvent.ANALYTICS.INSURENCE)
        mPresenter?.sendEventWithProduct(
            InsuranceTrackingEvent.EVENT_MANAGE_POLICY_SAVE_WARRANTY,
            InsuranceConstants.AnalyticsConstants.SUBPRODUCT_GAP,
            TrackingEvent.ANALYTICS.VVAPS_TITLE
        )
        Mockito.verify(mAnalytics)
            ?.sendEventActionWithMap(
                InsuranceTrackingEvent.EVENT_MANAGE_POLICY_SAVE_WARRANTY,
                cdata
            )
    }

    @Test
    fun sendEventWithProduct_shouldSendEventWithCorrectData() {
        val eventName = "test_event"
        val subProduct = "test_sub_product"
        mPresenter!!.sendEventWithProduct(
            eventName,
            subProduct,
            TrackingEvent.ANALYTICS.VVAPS_TITLE
        )
        verify(mAnalytics)?.sendEventActionWithMap(eq(eventName), any())
    }

    @Test
    fun sendEventWithProduct_shouldHandleNullEventNameAndSubProduct() {
        mPresenter!!.sendEventWithProduct(null, null, TrackingEvent.ANALYTICS.PL_COMBO_TITLE)
        verify(mAnalytics)?.sendEventActionWithMap(isNull(), any())
    }


    @Test
    fun fetchPremiumTest() {
        val policyNumber = "123456"
        val riskSerialNumber = "1"
        val mockResponse = FinancePremiumResponseDataModel()

        // Mock the helper to have a proper account view model
        val mockAccountViewModel = ClaimBankAccountViewModel()
        mockAccountViewModel.accountNumberId = "12345"
        `when`(mHelper?.accountViewModel).thenReturn(mockAccountViewModel)

        `when`(financeChangesRequestViewToDataMapper?.map(any())).thenReturn(
            mFinanceChangesRequestDataModel
        )
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.just(
                mockResponse
            )
        )

        mPresenter?.fetchPremium(mHelper!!, policyNumber, riskSerialNumber)

        verify(getPLPremiumForBankingChangeUseCase)?.execute(any())
        verify(mView, atLeastOnce())?.showProgressBar(true)
        verify(mView, atLeastOnce())?.showProgressBar(false)
    }

    @Test
    fun fetchPremiumFailureTest() {
        val policyNumber = "123456"
        val riskSerialNumber = "1"

        // Mock the helper to have a proper account view model
        val mockAccountViewModel = ClaimBankAccountViewModel()
        mockAccountViewModel.accountNumberId = "12345"
        `when`(mHelper?.accountViewModel).thenReturn(mockAccountViewModel)

        `when`(financeChangesRequestViewToDataMapper?.map(any())).thenReturn(
            mFinanceChangesRequestDataModel
        )
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.error(
                mThrowable
            )
        )

        mPresenter?.fetchPremium(mHelper!!, policyNumber, riskSerialNumber)

        verify(mView, atLeastOnce())?.showProgressBar(true)
        verify(mView, atLeastOnce())?.showProgressBar(false)
        verify(mView)?.showAPIError()
    }

    @Test
    fun updateBankingDetailsSuccessTest() {
        val policyNumber = "123456"
        val riskSerialNumber = "1"

        // Mock the helper to have a proper account view model
        val mockAccountViewModel = ClaimBankAccountViewModel()
        mockAccountViewModel.accountNumberId = "12345"
        `when`(mHelper?.accountViewModel).thenReturn(mockAccountViewModel)

        mockBankingResponseCode("R00")

        mPresenter?.updateBankingDetails(mHelper!!, policyNumber, riskSerialNumber)

        verify(updateBankingDetailsUseCase)?.execute(any())
        verify(mView, atLeastOnce())?.showProgressBar(true)
        verify(mView, atLeastOnce())?.showProgressBar(false)
        verify(mView)?.submitSuccess(mHelper!!)
    }

    @Test
    fun updateBankingDetailsR01Test() {
        val policyNumber = "123456"
        val riskSerialNumber = "1"

        // Mock the helper to have a proper account view model
        val mockAccountViewModel = ClaimBankAccountViewModel()
        mockAccountViewModel.accountNumberId = "12345"
        `when`(mHelper?.accountViewModel).thenReturn(mockAccountViewModel)

        mockBankingResponseCode("R01")

        mPresenter?.updateBankingDetails(mHelper!!, policyNumber, riskSerialNumber)

        verify(updateBankingDetailsUseCase)?.execute(any())
        verify(mView, atLeastOnce())?.showProgressBar(true)
        verify(mView, atLeastOnce())?.showProgressBar(false)
        verify(mView)?.goToBankingScreenWithError()
    }

    @Test
    fun updateBankingDetailsOtherErrorTest() {
        val policyNumber = "123456"
        val riskSerialNumber = "1"

        // Mock the helper to have a proper account view model
        val mockAccountViewModel = ClaimBankAccountViewModel()
        mockAccountViewModel.accountNumberId = "12345"
        `when`(mHelper?.accountViewModel).thenReturn(mockAccountViewModel)

        mockBankingResponseCode("R02")

        mPresenter?.updateBankingDetails(mHelper!!, policyNumber, riskSerialNumber)

        verify(updateBankingDetailsUseCase)?.execute(any())
        verify(mView, atLeastOnce())?.showProgressBar(true)
        verify(mView, atLeastOnce())?.showProgressBar(false)
        verify(mView)?.showSubmitError(mHelper!!)
    }

    @Test
    fun updateBankingDetailsFailureTest() {
        val policyNumber = "123456"
        val riskSerialNumber = "1"

        // Mock the helper to have a proper account view model
        val mockAccountViewModel = ClaimBankAccountViewModel()
        mockAccountViewModel.accountNumberId = "12345"
        `when`(mHelper?.accountViewModel).thenReturn(mockAccountViewModel)

        `when`(financeChangesRequestViewToDataMapper?.map(any())).thenReturn(
            mFinanceChangesRequestDataModel
        )
        `when`(updateBankingDetailsUseCase?.execute(any())).thenReturn(Observable.error(mThrowable))

        mPresenter?.updateBankingDetails(mHelper!!, policyNumber, riskSerialNumber)

        verify(mView, atLeastOnce())?.showProgressBar(true)
        verify(mView, atLeastOnce())?.showProgressBar(false)
        verify(mView)?.showSubmitError(mHelper!!)
    }

    @Test
    fun updateBankingDetails_resultSetNotNull_resultCodeNotNull() {
        val responseDataModel = FinanceChangesResponseDataModel()
        val resultSetViewModel = ResultSetViewModel()
        resultSetViewModel.resultCode = "R00"
        val responseViewModel = FinanceChangesResponseViewModel()
        responseViewModel.resultSet = resultSetViewModel
        val mockAccountViewModel = ClaimBankAccountViewModel()
        mockAccountViewModel.accountNumberId = "12345"

        `when`(mHelper?.accountViewModel).thenReturn(mockAccountViewModel)
        `when`(financeChangesResponseViewToDataMapper?.map(responseDataModel)).thenReturn(
            responseViewModel
        )
        `when`(updateBankingDetailsUseCase?.execute(any())).thenReturn(
            Observable.just(
                responseDataModel
            )
        )

        mPresenter!!.updateBankingDetails(mHelper!!, "POL123", "123")

        verify(mView, atLeastOnce())?.showProgressBar(true)
        verify(mView, atLeastOnce())?.showProgressBar(false)
        // Add more verifications as per the logic after this condition
    }

    @Test
    fun updateBankingDetails_resultSetNull() {
        val responseDataModel = FinanceChangesResponseDataModel()
        val responseViewModel = FinanceChangesResponseViewModel()
        responseViewModel.resultSet = null
        val mockAccountViewModel = ClaimBankAccountViewModel()
        mockAccountViewModel.accountNumberId = "12345"

        `when`(mHelper?.accountViewModel).thenReturn(mockAccountViewModel)
        `when`(financeChangesResponseViewToDataMapper?.map(responseDataModel)).thenReturn(
            responseViewModel
        )
        `when`(updateBankingDetailsUseCase?.execute(any())).thenReturn(
            Observable.just(
                responseDataModel
            )
        )

        mPresenter!!.updateBankingDetails(mHelper!!, "POL123", "123")

        verify(mView, atLeastOnce())?.showProgressBar(true)
        verify(mView, atLeastOnce())?.showProgressBar(false)
    }

    @Test
    fun updateBankingDetails_resultCodeNull() {
        val responseDataModel = FinanceChangesResponseDataModel()
        val resultSetViewModel = ResultSetViewModel()
        resultSetViewModel.resultCode = null
        val responseViewModel = FinanceChangesResponseViewModel()
        responseViewModel.resultSet = resultSetViewModel
        val mockAccountViewModel = ClaimBankAccountViewModel()
        mockAccountViewModel.accountNumberId = "12345"

        `when`(mHelper?.accountViewModel).thenReturn(mockAccountViewModel)
        `when`(financeChangesResponseViewToDataMapper?.map(responseDataModel)).thenReturn(
            responseViewModel
        )
        `when`(updateBankingDetailsUseCase?.execute(any())).thenReturn(
            Observable.just(
                responseDataModel
            )
        )

        mPresenter!!.updateBankingDetails(mHelper!!, "POL123", "123")

        verify(mView, atLeastOnce())?.showProgressBar(true)
        verify(mView, atLeastOnce())?.showProgressBar(false)
    }

    @Test
    fun updateBankingDetails_R01ResultCode_callsGoToBankingScreenWithError() {
        val responseDataModel = FinanceChangesResponseDataModel()
        val resultSetViewModel = ResultSetViewModel()
        resultSetViewModel.resultCode = InsuranceConstants.ResponseCode.CODE_R01
        val responseViewModel = FinanceChangesResponseViewModel()
        responseViewModel.resultSet = resultSetViewModel
        val mockAccountViewModel = ClaimBankAccountViewModel()
        mockAccountViewModel.accountNumberId = "12345"

        `when`(mHelper?.accountViewModel).thenReturn(mockAccountViewModel)
        `when`(financeChangesRequestViewToDataMapper?.map(any())).thenReturn(
            mFinanceChangesRequestDataModel
        )
        `when`(updateBankingDetailsUseCase?.execute(Mockito.any())).thenReturn(
            Observable.just(
                responseDataModel
            )
        )
        `when`(financeChangesResponseViewToDataMapper?.map(responseDataModel)).thenReturn(
            responseViewModel
        )

        mPresenter?.updateBankingDetails(mHelper!!, "POL123", "RISK123")

        verify(mView)?.goToBankingScreenWithError()
    }

    @Test
    fun updateBankingDetails_R01ResultCode_nullView_doesNotCrash() {
        mPresenter?.unbind()

        val responseDataModel = FinanceChangesResponseDataModel()
        val resultSetViewModel = ResultSetViewModel()
        resultSetViewModel.resultCode = InsuranceConstants.ResponseCode.CODE_R01
        val responseViewModel = FinanceChangesResponseViewModel()
        responseViewModel.resultSet = resultSetViewModel
        val mockAccountViewModel = ClaimBankAccountViewModel()
        mockAccountViewModel.accountNumberId = "12345"

        `when`(mHelper?.accountViewModel).thenReturn(mockAccountViewModel)
        `when`(financeChangesRequestViewToDataMapper?.map(any())).thenReturn(
            mFinanceChangesRequestDataModel
        )
        `when`(updateBankingDetailsUseCase?.execute(Mockito.any())).thenReturn(
            Observable.just(
                responseDataModel
            )
        )
        `when`(financeChangesResponseViewToDataMapper?.map(responseDataModel)).thenReturn(
            responseViewModel
        )

        mPresenter?.updateBankingDetails(mHelper!!, "POL123", "RISK123")

        verifyNoMoreInteractions(mView)
    }

    @Test
    fun showReviewPremiumScreenSuccessTest() {
        val isPolicyPl = true

        `when`(mNavigationRouter?.navigateTo(any())).thenReturn(true)

        mPresenter?.showReviewPremiumScreen(mHelper!!, isPolicyPl, "Hoc")

        verify(mNavigationRouter)?.navigateTo(any())
    }

    @Test
    fun showReviewPremiumScreenFailureTest() {
        val isPolicyPl = true

        `when`(mNavigationRouter?.navigateTo(any())).thenReturn(false)

        mPresenter?.showReviewPremiumScreen(mHelper!!, isPolicyPl, "HHC")

        verify(mNavigationRouter)?.navigateTo(any())
        // Note: Since the production method only does navigation, there's no failure handling in the method itself
    }

    @Test
    fun showReviewPremiumScreenNotOkTest() {
        val isPolicyPl = true
        `when`(mNavigationRouter?.navigateTo(any())).thenReturn(false)
        mPresenter?.showReviewPremiumScreen(mHelper!!, isPolicyPl, "MV")
        verify(mNavigationRouter)?.navigateTo(any())
        verify(mView)?.showProgressBar(false)
    }

    @Test
    fun filterAccountsForInsuranceTest() {
        // Test this indirectly through fetchNedbankAccountAndType which calls filterAccountsForInsurance
        val overview = DomainOverview()
        val accountsOverviewList = ArrayList<AccountsOverview>()

        // Create EVERYDAY_BANKING overview with insurance account list
        val everydayBankingOverview = AccountsOverview()
        everydayBankingOverview.overviewType = OverviewType.EVERYDAY_BANKING
        val insuranceAccountList = ArrayList<CoreAccountViewModel>()
        val account1 = CoreAccountViewModel()
        account1.accountNumber = "*********"
        insuranceAccountList.add(account1)
        everydayBankingOverview.insuranceAccountList = insuranceAccountList
        accountsOverviewList.add(everydayBankingOverview)

        overview.accountsOverviews = accountsOverviewList

        `when`(mGetAccountTypeList?.execute()).thenReturn(Observable.just(iSelectedViewModel))
        `when`(mGetOverviewUseCase?.execute()).thenReturn(
            Observable.just(
                CachableValue(
                    overview,
                    false,
                    false
                )
            )
        )
        `when`(mBankListUseCase?.execute()).thenReturn(Observable.just(emptyList()))

        mPresenter?.fetchNedbankAccountAndType()

        // Verify that the account list was processed
        verify(mView)?.setNedbankAccount(insuranceAccountList)
    }

    @Test
    fun showViewProgressBarTest() {
        mPresenter?.showViewProgressBar(true)
        verify(mView)?.showProgressBar(true)

        mPresenter?.showViewProgressBar(false)
        verify(mView)?.showProgressBar(false)
    }

    @Test
    fun showViewProgressBarNullViewTest() {
        mPresenter?.unbind()
        mPresenter?.showViewProgressBar(true)
        // Should not crash when view is null
    }

    @Test
    fun checkIfHavingMultipleBranchTest() {
        // Test the public method indirectly through fetchNedbankAccountAndType
        val bankDataModel1 = BankDataModel()
        bankDataModel1.universalCode = "123"
        val bankDataModel2 = BankDataModel()
        bankDataModel2.universalCode = "456"
        val bankList = listOf(bankDataModel1, bankDataModel2)

        `when`(mGetAccountTypeList?.execute()).thenReturn(Observable.just(iSelectedViewModel))
        `when`(mGetOverviewUseCase?.execute()).thenReturn(
            Observable.just(
                CachableValue(
                    DomainOverview(),
                    false,
                    false
                )
            )
        )
        `when`(mBankListUseCase?.execute()).thenReturn(Observable.just(bankList))

        mPresenter?.fetchNedbankAccountAndType()

        // Verify that the bank list was processed
        verify(mView)?.setAccountTypeList(any())
    }

    @Test
    fun checkIfHavingMultipleBranchDirectTest() {
        // Set up bank list in presenter
        val bankDataModel1 = BankDataModel()
        bankDataModel1.universalCode = "123"
        val bankDataModel2 = BankDataModel()
        bankDataModel2.universalCode = "456"
        val bankList = listOf(bankDataModel1, bankDataModel2)

        `when`(mGetAccountTypeList?.execute()).thenReturn(Observable.just(iSelectedViewModel))
        `when`(mGetOverviewUseCase?.execute()).thenReturn(
            Observable.just(
                CachableValue(
                    DomainOverview(),
                    false,
                    false
                )
            )
        )
        `when`(mBankListUseCase?.execute()).thenReturn(Observable.just(bankList))

        mPresenter?.fetchNedbankAccountAndType()

        // Test the method directly
        val result1 = mPresenter?.checkIfHavingMultipleBranch("123")
        Assert.assertTrue(result1 == true)

        val result2 = mPresenter?.checkIfHavingMultipleBranch("999")
        Assert.assertFalse(result2 == true)
    }

    @Test
    fun parsePremiumSuccessTest() {
        val mockDataModel = FinancePremiumResponseDataModel()
        val mockBankAccountViewModel = BankAccountViewModel()
        val mockHelper = EditBankingDetailsHelper(
            riskSerialNumber = "123",
            policyNumber = "POL123",
            oldPremium = 1000.0f,
            newPremium = 1500.0f,
            accountViewModel = mockBankAccountViewModel
        )

        // Create a complete mock response structure
        val mockViewModel =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.FinancePremiumResponseViewModel()
        val mockResultSet =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ResultSetViewModel()
        mockResultSet.resultCode = InsuranceConstants.ResponseCode.CODE_R00

        val mockPolicyOrder =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PolicyOrderViewModel()
        val mockPolicy =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PolicyViewModel()
        val mockPersonalVehicleSection =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PersonalVehicleItemSectionViewModel()
        val mockInsuranceAmountItem =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.InsuranceAmountItemViewModel()
        val mockContractAmountItem =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ContractAmountItemViewModel()
        val mockItemAmount =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ItemAmountViewModel()

        mockItemAmount.value = 1500.0f
        mockContractAmountItem.itemAmount = mockItemAmount
        mockInsuranceAmountItem.contractAmountItem = listOf(mockContractAmountItem)
        mockPersonalVehicleSection.insuranceAmountItem = listOf(mockInsuranceAmountItem)
        mockPolicy.personalVehicleItemSection = listOf(mockPersonalVehicleSection)
        mockPolicyOrder.policy = mockPolicy
        mockViewModel.policyOrder = mockPolicyOrder
        mockViewModel.resultSet = mockResultSet

        `when`(financePremiumResponseDataToViewMapper?.map(mockDataModel)).thenReturn(mockViewModel)
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.just(
                mockDataModel
            )
        )

        // Test parsePremium indirectly through fetchPremium
        mPresenter?.fetchPremium(mockHelper, "POL123", "123")

        verify(mView, Mockito.atLeastOnce())?.showProgressBar(true)
        verify(mView, Mockito.atLeastOnce())?.showProgressBar(false)
        verify(mView)?.setPremiumAmount(mockHelper)
    }

    @Test
    fun parsePremiumR01Test() {
        val mockDataModel = FinancePremiumResponseDataModel()
        val mockBankAccountViewModel = BankAccountViewModel()
        val mockHelper = EditBankingDetailsHelper(
            riskSerialNumber = "123",
            policyNumber = "POL123",
            oldPremium = 1000.0f,
            newPremium = 1500.0f,
            accountViewModel = mockBankAccountViewModel
        )

        val mockViewModel =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.FinancePremiumResponseViewModel()
        val mockResultSet =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ResultSetViewModel()
        mockResultSet.resultCode = InsuranceConstants.ResponseCode.CODE_R01
        mockViewModel.resultSet = mockResultSet

        `when`(financePremiumResponseDataToViewMapper?.map(mockDataModel)).thenReturn(mockViewModel)
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.just(
                mockDataModel
            )
        )

        // Test parsePremium with R01 response indirectly through fetchPremium
        mPresenter?.fetchPremium(mockHelper, "POL123", "123")

        verify(mView, Mockito.atLeastOnce())?.showProgressBar(true)
        verify(mView, Mockito.atLeastOnce())?.showProgressBar(false)
        verify(mView)?.showAPIError()
    }

    @Test
    fun parsePremiumErrorTest() {
        val mockDataModel = FinancePremiumResponseDataModel()
        val mockBankAccountViewModel = BankAccountViewModel()
        val mockHelper = EditBankingDetailsHelper(
            riskSerialNumber = "123",
            policyNumber = "POL123",
            oldPremium = 1000.0f,
            newPremium = 1500.0f,
            accountViewModel = mockBankAccountViewModel
        )

        val mockViewModel =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.FinancePremiumResponseViewModel()
        val mockResultSet =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ResultSetViewModel()
        mockResultSet.resultCode = "R99" // Unknown code
        mockViewModel.resultSet = mockResultSet

        `when`(financePremiumResponseDataToViewMapper?.map(mockDataModel)).thenReturn(mockViewModel)
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.just(
                mockDataModel
            )
        )

        // Test parsePremium with error response indirectly through fetchPremium
        mPresenter?.fetchPremium(mockHelper, "POL123", "123")

        verify(mView, Mockito.atLeastOnce())?.showProgressBar(true)
        verify(mView, Mockito.atLeastOnce())?.showProgressBar(false)
        verify(mView)?.showAPIError()
    }

    @Test
    fun handlePremiumResponseErrorTest() {
        val mockBankAccountViewModel = BankAccountViewModel()
        val mockHelper = EditBankingDetailsHelper(
            riskSerialNumber = "123",
            policyNumber = "POL123",
            oldPremium = 1000.0f,
            newPremium = 1500.0f,
            accountViewModel = mockBankAccountViewModel
        )

        // Test handlePremiumResponse with error scenario through fetchPremium
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.error(
                RuntimeException("Test error")
            )
        )

        mPresenter?.fetchPremium(mockHelper, "POL123", "123")

        verify(mView, Mockito.atLeastOnce())?.showProgressBar(true)
        verify(mView, Mockito.atLeastOnce())?.showProgressBar(false)
        verify(mView)?.showAPIError()
    }

    @Test
    fun handlePremiumResponseR01Test() {
        val mockDataModel = FinancePremiumResponseDataModel()
        val mockBankAccountViewModel = BankAccountViewModel()
        val mockHelper = EditBankingDetailsHelper(
            riskSerialNumber = "123",
            policyNumber = "POL123",
            oldPremium = 1000.0f,
            newPremium = 1500.0f,
            accountViewModel = mockBankAccountViewModel
        )

        val mockViewModel =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.FinancePremiumResponseViewModel()
        val mockResultSet =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ResultSetViewModel()
        mockResultSet.resultCode = InsuranceConstants.ResponseCode.CODE_R01
        mockViewModel.resultSet = mockResultSet

        `when`(financePremiumResponseDataToViewMapper?.map(mockDataModel)).thenReturn(mockViewModel)
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.just(
                mockDataModel
            )
        )

        // Test handlePremiumResponse with R01 indirectly through fetchPremium
        mPresenter?.fetchPremium(mockHelper, "POL123", "123")

        verify(mView, Mockito.atLeastOnce())?.showProgressBar(true)
        verify(mView, Mockito.atLeastOnce())?.showProgressBar(false)
        verify(mView)?.showAPIError()
    }

    @Test
    fun handlePremiumResponseInsuranceErrorTest() {
        val mockDataModel = FinancePremiumResponseDataModel()
        val mockBankAccountViewModel = BankAccountViewModel()
        val mockHelper = EditBankingDetailsHelper(
            riskSerialNumber = "123",
            policyNumber = "POL123",
            oldPremium = 1000.0f,
            newPremium = 1500.0f,
            accountViewModel = mockBankAccountViewModel
        )

        val mockViewModel =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.FinancePremiumResponseViewModel()
        val mockResultSet =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ResultSetViewModel()
        mockResultSet.resultCode = "R99" // Will result in INSURANCE_ERROR
        mockViewModel.resultSet = mockResultSet

        `when`(financePremiumResponseDataToViewMapper?.map(mockDataModel)).thenReturn(mockViewModel)
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.just(
                mockDataModel
            )
        )

        // Test handlePremiumResponse with insurance error indirectly through fetchPremium
        mPresenter?.fetchPremium(mockHelper, "POL123", "123")

        verify(mView, Mockito.atLeastOnce())?.showProgressBar(true)
        verify(mView, Mockito.atLeastOnce())?.showProgressBar(false)
        verify(mView)?.showAPIError()
    }

    // NEW TESTS FOR IMPROVED COVERAGE

    @Test
    fun handlePremiumResponseNullDataModelTest() {
        // Test Line 130: showAPIError() when dataModel is null
        val mockBankAccountViewModel = BankAccountViewModel()
        val mockHelper = EditBankingDetailsHelper(
            riskSerialNumber = "123",
            policyNumber = "POL123",
            oldPremium = 1000.0f,
            newPremium = 1500.0f,
            accountViewModel = mockBankAccountViewModel
        )

        `when`(financeChangesRequestViewToDataMapper?.map(any())).thenReturn(
            mFinanceChangesRequestDataModel
        )
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.error(
                RuntimeException("Test error")
            )
        )

        mPresenter?.fetchPremium(mockHelper, "POL123", "123")

        verify(mView, Mockito.atLeastOnce())?.showProgressBar(true)
        verify(mView, Mockito.atLeastOnce())?.showProgressBar(false)
        verify(mView)?.showAPIError()
    }

    @Test
    fun parsePremiumNullResultSetTest() {
        // Test Lines 140-141: null resultSet scenario
        val mockDataModel = FinancePremiumResponseDataModel()
        val mockBankAccountViewModel = BankAccountViewModel()
        val mockHelper = EditBankingDetailsHelper(
            riskSerialNumber = "123",
            policyNumber = "POL123",
            oldPremium = 1000.0f,
            newPremium = 1500.0f,
            accountViewModel = mockBankAccountViewModel
        )

        val mockViewModel =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.FinancePremiumResponseViewModel()
        mockViewModel.resultSet = null // Null resultSet

        `when`(financePremiumResponseDataToViewMapper?.map(mockDataModel)).thenReturn(mockViewModel)
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.just(
                mockDataModel
            )
        )

        mPresenter?.fetchPremium(mockHelper, "POL123", "123")

        verify(mView, Mockito.atLeastOnce())?.showProgressBar(true)
        verify(mView, Mockito.atLeastOnce())?.showProgressBar(false)
        verify(mView)?.showAPIError()
    }

    @Test
    fun parsePremiumNullResultCodeTest() {
        // Test Lines 140-141: null resultCode scenario
        val mockDataModel = FinancePremiumResponseDataModel()
        val mockBankAccountViewModel = BankAccountViewModel()
        val mockHelper = EditBankingDetailsHelper(
            riskSerialNumber = "123",
            policyNumber = "POL123",
            oldPremium = 1000.0f,
            newPremium = 1500.0f,
            accountViewModel = mockBankAccountViewModel
        )

        val mockViewModel =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.FinancePremiumResponseViewModel()
        val mockResultSet =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ResultSetViewModel()
        mockResultSet.resultCode = null // Null resultCode
        mockViewModel.resultSet = mockResultSet

        `when`(financePremiumResponseDataToViewMapper?.map(mockDataModel)).thenReturn(mockViewModel)
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.just(
                mockDataModel
            )
        )

        mPresenter?.fetchPremium(mockHelper, "POL123", "123")

        verify(mView, Mockito.atLeastOnce())?.showProgressBar(true)
        verify(mView, Mockito.atLeastOnce())?.showProgressBar(false)
        verify(mView)?.showAPIError()
    }

    @Test
    fun parsePremiumNullPolicyOrderTest() {
        // Test Lines 147-150: null policyOrder scenario
        val mockDataModel = FinancePremiumResponseDataModel()
        val mockBankAccountViewModel = BankAccountViewModel()
        val mockHelper = EditBankingDetailsHelper(
            riskSerialNumber = "123",
            policyNumber = "POL123",
            oldPremium = 1000.0f,
            newPremium = 1500.0f,
            accountViewModel = mockBankAccountViewModel
        )

        val mockViewModel =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.FinancePremiumResponseViewModel()
        val mockResultSet =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ResultSetViewModel()
        mockResultSet.resultCode = InsuranceConstants.ResponseCode.CODE_R00
        mockViewModel.resultSet = mockResultSet
        mockViewModel.policyOrder = null // Null policyOrder

        `when`(financePremiumResponseDataToViewMapper?.map(mockDataModel)).thenReturn(mockViewModel)
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.just(
                mockDataModel
            )
        )

        mPresenter?.fetchPremium(mockHelper, "POL123", "123")

        verify(mView, Mockito.atLeastOnce())?.showProgressBar(true)
        verify(mView, Mockito.atLeastOnce())?.showProgressBar(false)
        verify(mView)?.showAPIError()
    }

    @Test
    fun parsePremiumNullPolicyTest() {
        // Test Lines 147-150: null policy scenario
        val mockDataModel = FinancePremiumResponseDataModel()
        val mockBankAccountViewModel = BankAccountViewModel()
        val mockHelper = EditBankingDetailsHelper(
            riskSerialNumber = "123",
            policyNumber = "POL123",
            oldPremium = 1000.0f,
            newPremium = 1500.0f,
            accountViewModel = mockBankAccountViewModel
        )

        val mockViewModel =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.FinancePremiumResponseViewModel()
        val mockResultSet =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ResultSetViewModel()
        mockResultSet.resultCode = InsuranceConstants.ResponseCode.CODE_R00
        mockViewModel.resultSet = mockResultSet

        val mockPolicyOrder =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PolicyOrderViewModel()
        mockPolicyOrder.policy = null // Null policy
        mockViewModel.policyOrder = mockPolicyOrder

        `when`(financePremiumResponseDataToViewMapper?.map(mockDataModel)).thenReturn(mockViewModel)
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.just(
                mockDataModel
            )
        )

        mPresenter?.fetchPremium(mockHelper, "POL123", "123")

        verify(mView, Mockito.atLeastOnce())?.showProgressBar(true)
        verify(mView, Mockito.atLeastOnce())?.showProgressBar(false)
        verify(mView)?.showAPIError()
    }

    @Test
    fun parsePremiumEmptyPersonalVehicleItemSectionTest() {
        // Test Lines 147-150: empty personalVehicleItemSection scenario
        val mockDataModel = FinancePremiumResponseDataModel()
        val mockBankAccountViewModel = BankAccountViewModel()
        val mockHelper = EditBankingDetailsHelper(
            riskSerialNumber = "123",
            policyNumber = "POL123",
            oldPremium = 1000.0f,
            newPremium = 1500.0f,
            accountViewModel = mockBankAccountViewModel
        )

        val mockViewModel =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.FinancePremiumResponseViewModel()
        val mockResultSet =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ResultSetViewModel()
        mockResultSet.resultCode = InsuranceConstants.ResponseCode.CODE_R00
        mockViewModel.resultSet = mockResultSet

        val mockPolicyOrder =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PolicyOrderViewModel()
        val mockPolicy =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PolicyViewModel()
        mockPolicy.personalVehicleItemSection = emptyList() // Empty list
        mockPolicyOrder.policy = mockPolicy
        mockViewModel.policyOrder = mockPolicyOrder

        `when`(financePremiumResponseDataToViewMapper?.map(mockDataModel)).thenReturn(mockViewModel)
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.just(
                mockDataModel
            )
        )

        mPresenter?.fetchPremium(mockHelper, "POL123", "123")

        verify(mView, Mockito.atLeastOnce())?.showProgressBar(true)
        verify(mView, Mockito.atLeastOnce())?.showProgressBar(false)
        verify(mView)?.showAPIError()
    }

    @Test
    fun parsePremiumEmptyInsuranceAmountItemTest() {
        // Test Lines 152, 154-155: empty insuranceAmountItem scenario
        val mockDataModel = FinancePremiumResponseDataModel()
        val mockBankAccountViewModel = BankAccountViewModel()
        val mockHelper = EditBankingDetailsHelper(
            riskSerialNumber = "123",
            policyNumber = "POL123",
            oldPremium = 1000.0f,
            newPremium = 1500.0f,
            accountViewModel = mockBankAccountViewModel
        )

        val mockViewModel =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.FinancePremiumResponseViewModel()
        val mockResultSet =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ResultSetViewModel()
        mockResultSet.resultCode = InsuranceConstants.ResponseCode.CODE_R00
        mockViewModel.resultSet = mockResultSet

        val mockPolicyOrder =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PolicyOrderViewModel()
        val mockPolicy =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PolicyViewModel()
        val mockPersonalVehicleSection =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PersonalVehicleItemSectionViewModel()
        mockPersonalVehicleSection.insuranceAmountItem = emptyList() // Empty insuranceAmountItem
        mockPolicy.personalVehicleItemSection = listOf(mockPersonalVehicleSection)
        mockPolicyOrder.policy = mockPolicy
        mockViewModel.policyOrder = mockPolicyOrder

        `when`(financePremiumResponseDataToViewMapper?.map(mockDataModel)).thenReturn(mockViewModel)
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.just(
                mockDataModel
            )
        )

        mPresenter?.fetchPremium(mockHelper, "POL123", "123")

        verify(mView, Mockito.atLeastOnce())?.showProgressBar(true)
        verify(mView, Mockito.atLeastOnce())?.showProgressBar(false)
        verify(mView)?.showAPIError()
    }

    @Test
    fun parsePremiumEmptyContractAmountItemTest() {
        // Test Lines 154-155: empty contractAmountItem scenario
        val mockDataModel = FinancePremiumResponseDataModel()
        val mockBankAccountViewModel = BankAccountViewModel()
        val mockHelper = EditBankingDetailsHelper(
            riskSerialNumber = "123",
            policyNumber = "POL123",
            oldPremium = 1000.0f,
            newPremium = 1500.0f,
            accountViewModel = mockBankAccountViewModel
        )

        val mockViewModel =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.FinancePremiumResponseViewModel()
        val mockResultSet =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ResultSetViewModel()
        mockResultSet.resultCode = InsuranceConstants.ResponseCode.CODE_R00
        mockViewModel.resultSet = mockResultSet

        val mockPolicyOrder =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PolicyOrderViewModel()
        val mockPolicy =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PolicyViewModel()
        val mockPersonalVehicleSection =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PersonalVehicleItemSectionViewModel()
        val mockInsuranceAmountItem =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.InsuranceAmountItemViewModel()
        mockInsuranceAmountItem.contractAmountItem = emptyList() // Empty contractAmountItem
        mockPersonalVehicleSection.insuranceAmountItem = listOf(mockInsuranceAmountItem)
        mockPolicy.personalVehicleItemSection = listOf(mockPersonalVehicleSection)
        mockPolicyOrder.policy = mockPolicy
        mockViewModel.policyOrder = mockPolicyOrder

        `when`(financePremiumResponseDataToViewMapper?.map(mockDataModel)).thenReturn(mockViewModel)
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.just(
                mockDataModel
            )
        )

        mPresenter?.fetchPremium(mockHelper, "POL123", "123")

        verify(mView, Mockito.atLeastOnce())?.showProgressBar(true)
        verify(mView, Mockito.atLeastOnce())?.showProgressBar(false)
        verify(mView)?.showAPIError()
    }

    @Test
    fun parsePremiumNullItemAmountTest() {
        // Test Lines 160, 162, 164: null itemAmount scenario
        val mockDataModel = FinancePremiumResponseDataModel()
        val mockBankAccountViewModel = BankAccountViewModel()
        val mockHelper = EditBankingDetailsHelper(
            riskSerialNumber = "123",
            policyNumber = "POL123",
            oldPremium = 1000.0f,
            newPremium = 1500.0f,
            accountViewModel = mockBankAccountViewModel
        )

        val mockViewModel =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.FinancePremiumResponseViewModel()
        val mockResultSet =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ResultSetViewModel()
        mockResultSet.resultCode = InsuranceConstants.ResponseCode.CODE_R00
        mockViewModel.resultSet = mockResultSet

        val mockPolicyOrder =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PolicyOrderViewModel()
        val mockPolicy =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PolicyViewModel()
        val mockPersonalVehicleSection =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PersonalVehicleItemSectionViewModel()
        val mockInsuranceAmountItem =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.InsuranceAmountItemViewModel()
        val mockContractAmountItem =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ContractAmountItemViewModel()
        mockContractAmountItem.itemAmount = null // Null itemAmount
        mockInsuranceAmountItem.contractAmountItem = listOf(mockContractAmountItem)
        mockPersonalVehicleSection.insuranceAmountItem = listOf(mockInsuranceAmountItem)
        mockPolicy.personalVehicleItemSection = listOf(mockPersonalVehicleSection)
        mockPolicyOrder.policy = mockPolicy
        mockViewModel.policyOrder = mockPolicyOrder

        `when`(financePremiumResponseDataToViewMapper?.map(mockDataModel)).thenReturn(mockViewModel)
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.just(
                mockDataModel
            )
        )

        mPresenter?.fetchPremium(mockHelper, "POL123", "123")

        verify(mView, Mockito.atLeastOnce())?.showProgressBar(true)
        verify(mView, Mockito.atLeastOnce())?.showProgressBar(false)
        verify(mView)?.showAPIError()
    }

    @Test
    fun parsePremiumNullItemAmountValueTest() {
        // Test Lines 160, 162, 164: null itemAmount.value scenario
        val mockDataModel = FinancePremiumResponseDataModel()
        val mockBankAccountViewModel = BankAccountViewModel()
        val mockHelper = EditBankingDetailsHelper(
            riskSerialNumber = "123",
            policyNumber = "POL123",
            oldPremium = 1000.0f,
            newPremium = 1500.0f,
            accountViewModel = mockBankAccountViewModel
        )

        val mockViewModel =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.FinancePremiumResponseViewModel()
        val mockResultSet =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ResultSetViewModel()
        mockResultSet.resultCode = InsuranceConstants.ResponseCode.CODE_R00
        mockViewModel.resultSet = mockResultSet

        val mockPolicyOrder =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PolicyOrderViewModel()
        val mockPolicy =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PolicyViewModel()
        val mockPersonalVehicleSection =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PersonalVehicleItemSectionViewModel()
        val mockInsuranceAmountItem =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.InsuranceAmountItemViewModel()
        val mockContractAmountItem =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ContractAmountItemViewModel()
        val mockItemAmount =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ItemAmountViewModel()
        mockItemAmount.value = null // Null value
        mockContractAmountItem.itemAmount = mockItemAmount
        mockInsuranceAmountItem.contractAmountItem = listOf(mockContractAmountItem)
        mockPersonalVehicleSection.insuranceAmountItem = listOf(mockInsuranceAmountItem)
        mockPolicy.personalVehicleItemSection = listOf(mockPersonalVehicleSection)
        mockPolicyOrder.policy = mockPolicy
        mockViewModel.policyOrder = mockPolicyOrder

        `when`(financePremiumResponseDataToViewMapper?.map(mockDataModel)).thenReturn(mockViewModel)
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.just(
                mockDataModel
            )
        )

        mPresenter?.fetchPremium(mockHelper, "POL123", "123")

        verify(mView, Mockito.atLeastOnce())?.showProgressBar(true)
        verify(mView, Mockito.atLeastOnce())?.showProgressBar(false)
        verify(mView)?.showAPIError()
    }

    @Test
    fun parsePremiumValidPremiumAmountTest() {
        // Test Lines 167, 169-171: valid premium amount scenario
        val mockDataModel = FinancePremiumResponseDataModel()
        val mockBankAccountViewModel = BankAccountViewModel()
        val mockHelper = EditBankingDetailsHelper(
            riskSerialNumber = "123",
            policyNumber = "POL123",
            oldPremium = 1000.0f,
            newPremium = 1500.0f,
            accountViewModel = mockBankAccountViewModel
        )

        val mockViewModel =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.FinancePremiumResponseViewModel()
        val mockResultSet =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ResultSetViewModel()
        mockResultSet.resultCode = InsuranceConstants.ResponseCode.CODE_R00
        mockViewModel.resultSet = mockResultSet

        val mockPolicyOrder =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PolicyOrderViewModel()
        val mockPolicy =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PolicyViewModel()
        val mockPersonalVehicleSection =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.PersonalVehicleItemSectionViewModel()
        val mockInsuranceAmountItem =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.InsuranceAmountItemViewModel()
        val mockContractAmountItem =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ContractAmountItemViewModel()
        val mockItemAmount =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ItemAmountViewModel()
        mockItemAmount.value = 2000.0f // Valid premium amount
        mockContractAmountItem.itemAmount = mockItemAmount
        mockInsuranceAmountItem.contractAmountItem = listOf(mockContractAmountItem)
        mockPersonalVehicleSection.insuranceAmountItem = listOf(mockInsuranceAmountItem)
        mockPolicy.personalVehicleItemSection = listOf(mockPersonalVehicleSection)
        mockPolicyOrder.policy = mockPolicy
        mockViewModel.policyOrder = mockPolicyOrder

        `when`(financePremiumResponseDataToViewMapper?.map(mockDataModel)).thenReturn(mockViewModel)
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.just(
                mockDataModel
            )
        )

        mPresenter?.fetchPremium(mockHelper, "POL123", "123")

        verify(mView, Mockito.atLeastOnce())?.showProgressBar(true)
        verify(mView, Mockito.atLeastOnce())?.showProgressBar(false)
        verify(mView)?.setPremiumAmount(mockHelper)
        // Verify that the helper's newPremium was set
        Assert.assertEquals(2000.0f, mockHelper.newPremium)
    }

    @Test
    fun parsePremiumR01ResultSetTest() {
        // Test Lines 174-175: R01 result set validation
        val mockDataModel = FinancePremiumResponseDataModel()
        val mockBankAccountViewModel = BankAccountViewModel()
        val mockHelper = EditBankingDetailsHelper(
            riskSerialNumber = "123",
            policyNumber = "POL123",
            oldPremium = 1000.0f,
            newPremium = 1500.0f,
            accountViewModel = mockBankAccountViewModel
        )

        val mockViewModel =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.FinancePremiumResponseViewModel()
        val mockResultSet =
            za.co.nedbank.services.insurance.view.other.model.response.vvap.manage_policy.finance.fetch_premium.ResultSetViewModel()
        mockResultSet.resultCode = InsuranceConstants.ResponseCode.CODE_R01
        mockViewModel.resultSet = mockResultSet

        `when`(financePremiumResponseDataToViewMapper?.map(mockDataModel)).thenReturn(mockViewModel)
        `when`(getPLPremiumForBankingChangeUseCase?.execute(any())).thenReturn(
            Observable.just(
                mockDataModel
            )
        )

        mPresenter?.fetchPremium(mockHelper, "POL123", "123")

        verify(mView, Mockito.atLeastOnce())?.showProgressBar(true)
        verify(mView, Mockito.atLeastOnce())?.showProgressBar(false)
        verify(mView)?.showAPIError()
    }

    @Test
    fun filterAccountsForInsuranceNullOverviewTest() {
        // Test Line 252: null overview scenario
        `when`(mGetAccountTypeList?.execute()).thenReturn(Observable.just(iSelectedViewModel))
        `when`(mGetOverviewUseCase?.execute()).thenReturn(
            Observable.just(
                CachableValue(
                    null, // Null overview
                    false,
                    false
                )
            )
        )
        `when`(mBankListUseCase?.execute()).thenReturn(Observable.just(emptyList()))

        mPresenter?.fetchNedbankAccountAndType()

        // Verify that setAccountTypeList is called but setNedbankAccount is not called
        verify(mView)?.setAccountTypeList(any())
        verify(mView, never())?.setNedbankAccount(any())
    }

    @Test
    fun filterAccountsForInsuranceNullAccountsOverviewsTest() {
        // Test Line 252: null accountsOverviews scenario
        val overview = DomainOverview()
        overview.accountsOverviews = null // Null accountsOverviews

        `when`(mGetAccountTypeList?.execute()).thenReturn(Observable.just(iSelectedViewModel))
        `when`(mGetOverviewUseCase?.execute()).thenReturn(
            Observable.just(
                CachableValue(
                    overview,
                    false,
                    false
                )
            )
        )
        `when`(mBankListUseCase?.execute()).thenReturn(Observable.just(emptyList()))

        mPresenter?.fetchNedbankAccountAndType()

        // Verify that setAccountTypeList is called but setNedbankAccount is not called
        verify(mView)?.setAccountTypeList(any())
        verify(mView, never())?.setNedbankAccount(any())
    }

    @Test
    fun filterAccountsForInsuranceNonEverydayBankingTest() {
        // Test Line 248: non-EVERYDAY_BANKING overview type scenario
        val overview = DomainOverview()
        val accountsOverviewList = ArrayList<AccountsOverview>()

        // Create non-EVERYDAY_BANKING overview
        val creditCardsOverview = AccountsOverview()
        creditCardsOverview.overviewType = OverviewType.CREDIT_CARDS // Not EVERYDAY_BANKING
        val insuranceAccountList = ArrayList<CoreAccountViewModel>()
        val account1 = CoreAccountViewModel()
        account1.accountNumber = "*********"
        insuranceAccountList.add(account1)
        creditCardsOverview.insuranceAccountList = insuranceAccountList
        accountsOverviewList.add(creditCardsOverview)

        overview.accountsOverviews = accountsOverviewList

        `when`(mGetAccountTypeList?.execute()).thenReturn(Observable.just(iSelectedViewModel))
        `when`(mGetOverviewUseCase?.execute()).thenReturn(
            Observable.just(
                CachableValue(
                    overview,
                    false,
                    false
                )
            )
        )
        `when`(mBankListUseCase?.execute()).thenReturn(Observable.just(emptyList()))

        mPresenter?.fetchNedbankAccountAndType()

        // Verify that setAccountTypeList is called but setNedbankAccount is not called with the insurance account list
        verify(mView)?.setAccountTypeList(any())
        verify(mView)?.setNedbankAccount(null)
    }

    @Test
    fun filterAccountsForInsuranceNullInsuranceAccountListTest() {
        // Test Line 248: EVERYDAY_BANKING with null insuranceAccountList scenario
        val overview = DomainOverview()
        val accountsOverviewList = ArrayList<AccountsOverview>()

        // Create EVERYDAY_BANKING overview with null insurance account list
        val everydayBankingOverview = AccountsOverview()
        everydayBankingOverview.overviewType = OverviewType.EVERYDAY_BANKING
        everydayBankingOverview.insuranceAccountList = null // Null insurance account list
        accountsOverviewList.add(everydayBankingOverview)

        overview.accountsOverviews = accountsOverviewList

        `when`(mGetAccountTypeList?.execute()).thenReturn(Observable.just(iSelectedViewModel))
        `when`(mGetOverviewUseCase?.execute()).thenReturn(
            Observable.just(
                CachableValue(
                    overview,
                    false,
                    false
                )
            )
        )
        `when`(mBankListUseCase?.execute()).thenReturn(Observable.just(emptyList()))

        mPresenter?.fetchNedbankAccountAndType()

        // Verify that setAccountTypeList is called but setNedbankAccount is not called
        verify(mView)?.setAccountTypeList(any())
        verify(mView)?.setNedbankAccount(null)
    }

    @Test
    fun validateAllFieldsNullViewTest() {
        // Test Line 275: null view scenario
        mPresenter?.unbind() // Unbind view to make it null

        `when`(mSuccessResult!!.isOk).thenReturn(true)
        `when`(mNonEmptyTextValidator!!.validateInput(ArgumentMatchers.any()))
            .thenReturn(mSuccessResult)
        `when`(bankNameValidatableInput!!.value).thenReturn("abc")
        `when`(branchCodeValidatableInput!!.value).thenReturn("cde")
        `when`(accountTypeValidatableInput!!.value).thenReturn("fgh")
        `when`(accountNoValidatableInput!!.value).thenReturn("*********")

        mPresenter?.validateAllFields(
            bankNameValidatableInput!!,
            branchCodeValidatableInput!!,
            accountTypeValidatableInput!!,
            accountNoValidatableInput!!,
            true,
            true
        )

        // Should not crash and no view interactions should occur
        verifyNoMoreInteractions(mView)
    }

    @Test
    fun validateAllFieldsAccountNumberTrimmingTest() {
        // Test Line 279: account number trimming logic
        `when`(mSuccessResult!!.isOk).thenReturn(true)
        `when`(mNonEmptyTextValidator!!.validateInput(ArgumentMatchers.any()))
            .thenReturn(mSuccessResult)
        `when`(bankNameValidatableInput!!.value).thenReturn("abc")
        `when`(branchCodeValidatableInput!!.value).thenReturn("cde")
        `when`(accountTypeValidatableInput!!.value).thenReturn("fgh")
        `when`(accountNoValidatableInput!!.value).thenReturn("  *********  ") // Account number with spaces

        mPresenter?.validateAllFields(
            bankNameValidatableInput!!,
            branchCodeValidatableInput!!,
            accountTypeValidatableInput!!,
            accountNoValidatableInput!!,
            true,
            true
        )

        // Verify that trimming logic is executed and validation passes
        verify(mView)?.setNextButtonEnable(true)
    }

    @Test
    fun validateAllFieldsAccountNumberLengthErrorTest() {
        // Test Line 282: account number length error scenario
        `when`(mSuccessResult!!.isOk).thenReturn(true)
        `when`(mNonEmptyTextValidator!!.validateInput(ArgumentMatchers.any()))
            .thenReturn(mSuccessResult)
        `when`(bankNameValidatableInput!!.value).thenReturn("abc")
        `when`(branchCodeValidatableInput!!.value).thenReturn("cde")
        `when`(accountTypeValidatableInput!!.value).thenReturn("fgh")
        `when`(accountNoValidatableInput!!.value).thenReturn("1234567") // Less than 8 characters

        mPresenter?.validateAllFields(
            bankNameValidatableInput!!,
            branchCodeValidatableInput!!,
            accountTypeValidatableInput!!,
            accountNoValidatableInput!!,
            true,
            true
        )

        // Verify that account text error is shown and next button is disabled
        verify(mView)?.showAccountTextError()
        verify(mView)?.setNextButtonEnable(false)
    }

    @Test
    fun validateAllFieldsSetNextButtonEnableTest() {
        // Test Line 285: setNextButtonEnable call
        `when`(mSuccessResult!!.isOk).thenReturn(true)
        `when`(mNonEmptyTextValidator!!.validateInput(ArgumentMatchers.any()))
            .thenReturn(mSuccessResult)
        `when`(bankNameValidatableInput!!.value).thenReturn("abc")
        `when`(branchCodeValidatableInput!!.value).thenReturn("cde")
        `when`(accountTypeValidatableInput!!.value).thenReturn("fgh")
        `when`(accountNoValidatableInput!!.value).thenReturn("*********")

        mPresenter?.validateAllFields(
            bankNameValidatableInput!!,
            branchCodeValidatableInput!!,
            accountTypeValidatableInput!!,
            accountNoValidatableInput!!,
            true,
            true
        )

        // Verify that setNextButtonEnable is called with true
        verify(mView)?.setNextButtonEnable(true)
    }

    @Test
    fun setViewSelectedBranchNullNavigationResultTest() {
        // Test Line 327: null navigationResult scenario
        mPresenter?.setViewSelectedBranch(null)

        // Should not crash and no view interactions should occur
        verifyNoMoreInteractions(mView)
    }

    @Test
    fun setViewSelectedBranchNotOkNavigationResultTest() {
        // Test Line 327: navigationResult.isOk = false scenario
        val params: MutableMap<String, Any> = HashMap()
        val navigationResult = NavigationResult(false, params) // isOk = false

        mPresenter?.setViewSelectedBranch(navigationResult)

        // Should not crash and no view interactions should occur
        verifyNoMoreInteractions(mView)
    }

    @Test
    fun setViewSelectedBranchSuccessTest() {
        // Test Line 332: successful branch selection scenario
        val branchViewModel = BankBranchViewModel()
        branchViewModel.branchCode = "123"
        branchViewModel.branchName = "Test Branch"
        val params: MutableMap<String, Any> = HashMap()
        params[InsuranceConstants.BundleKeys.SELECTED_BRANCH_MODEL] = branchViewModel
        val navigationResult = NavigationResult(true, params)

        mPresenter?.setViewSelectedBranch(navigationResult)

        // Verify that setSelectedBranch is called
        verify(mView)?.setSelectedBranch(branchViewModel)
    }

    @Test
    fun handleAccountTypeClickParameterSettingTest() {
        // Test Line 346: parameter setting for common selection list
        val accountTypeList = ArrayList<ISelectedViewModel>()
        val model = InsuranceCodeDescriptionViewModel()
        model.code = "SA"
        model.description = "Savings Account"
        accountTypeList.add(model)

        `when`(mView?.getAccountTypeList()).thenReturn(accountTypeList)

        val params: MutableMap<String, Any> = HashMap()
        params[InsuranceConstants.ParamKeys.PARAM_SELECTED_TYPE_MODEL] = model
        val navigationResult = NavigationResult(true, params)
        `when`(mNavigationRouter!!.navigateWithResult(ArgumentMatchers.any()))
            .thenReturn(Observable.just(navigationResult))

        mPresenter?.handleAccountTypeClick()

        // Verify that navigation is called and account type data is set
        verify(mNavigationRouter)?.navigateWithResult(ArgumentMatchers.any())
        verify(mView)?.setAccountTypeData(model)
    }

    @Test
    fun handleAccountTypeClick_shouldPassAccountTypeListToNavigation() {
        val accountTypeList = listOf(InsuranceCodeDescriptionViewModel().apply {
            code = "SA"
            description = "Savings Account"
        })
        val params: MutableMap<String, Any> = HashMap()
        params[InsuranceConstants.ParamKeys.PARAM_COMMON_SELECTION_LIST] = accountTypeList
        val navigationResult = NavigationResult(true, params)
        `when`(mNavigationRouter!!.navigateWithResult(ArgumentMatchers.any()))
            .thenReturn(Observable.just(navigationResult))
        `when`(mView?.getAccountTypeList()).thenReturn(accountTypeList)

        mPresenter?.handleAccountTypeClick()

        verify(mNavigationRouter)?.navigateWithResult(any())
    }

    @Test
    fun handleAccountTypeClick_shouldHandleNullAccountTypeList() {
        val params: MutableMap<String, Any> = HashMap()
        params[InsuranceConstants.ParamKeys.PARAM_COMMON_SELECTION_LIST] = emptyList<Any>()
        val navigationResult = NavigationResult(false, params)
        `when`(mNavigationRouter!!.navigateWithResult(ArgumentMatchers.any()))
            .thenReturn(Observable.just(navigationResult))
        `when`(mView?.getAccountTypeList()).thenReturn(null)

        mPresenter?.handleAccountTypeClick()

       verify(mNavigationRouter)?.navigateWithResult(any())
    }

    @Test
    fun getRequestViewModelPropertyAssignmentsTest() {
        // Test Lines 392-394: CDV request view model property assignments
        val mockAccountType = InsuranceCodeDescriptionViewModel()
        mockAccountType.code = "SA"

        `when`(mView?.getAccountNumber()).thenReturn("*********")
        `when`(mView?.getSelectedAccountType()).thenReturn(mockAccountType)
        `when`(mView?.getBranchCode()).thenReturn("123456")

        val result = mPresenter?.getRequestViewModel()

        // Verify that the request view model is properly populated
        Assert.assertNotNull(result)
        Assert.assertEquals(1, result?.size)
        val cdvRequest = result?.get(0)
        Assert.assertEquals("*********", cdvRequest?.accountNumber)
        Assert.assertEquals("SA", cdvRequest?.accountType)
        Assert.assertEquals(123456, cdvRequest?.cdvSortCode)
        Assert.assertEquals(InsuranceConstants.CDV_FUNCTION_VALUE, cdvRequest?.function)
    }

    @Test
    fun getRequestViewModelNullBranchCodeTest() {
        // Test Lines 392-394: null branch code scenario
        val mockAccountType = InsuranceCodeDescriptionViewModel()
        mockAccountType.code = "SA"

        `when`(mView?.getAccountNumber()).thenReturn("*********")
        `when`(mView?.getSelectedAccountType()).thenReturn(mockAccountType)
        `when`(mView?.getBranchCode()).thenReturn(null) // Null branch code

        val result = mPresenter?.getRequestViewModel()

        // Verify that the request view model handles null branch code
        Assert.assertNotNull(result)
        Assert.assertEquals(1, result?.size)
        val cdvRequest = result?.get(0)
        Assert.assertEquals("*********", cdvRequest?.accountNumber)
        Assert.assertEquals("SA", cdvRequest?.accountType)
        Assert.assertEquals(0, cdvRequest?.cdvSortCode) // Should default to Constants.ZERO
        Assert.assertEquals(InsuranceConstants.CDV_FUNCTION_VALUE, cdvRequest?.function)
    }

    @Test
    fun getRequestViewModelNullAccountNumberTest() {
        // accountNumber is null
        `when`(mView?.getAccountNumber()).thenReturn(null)
        `when`(mView?.getSelectedAccountType()).thenReturn(null)
        `when`(mView?.getBranchCode()).thenReturn("123456")
        val result = mPresenter?.getRequestViewModel()
        Assert.assertNotNull(result)
        Assert.assertEquals(1, result?.size)
        val cdvRequest = result?.get(0)
        Assert.assertNull(cdvRequest?.accountNumber)
        Assert.assertNull(cdvRequest?.accountType)
        Assert.assertEquals(123456, cdvRequest?.cdvSortCode)
        Assert.assertEquals(InsuranceConstants.CDV_FUNCTION_VALUE, cdvRequest?.function)
    }

    @Test
    fun getRequestViewModelNullAccountTypeTest() {
        // accountType is null
        `when`(mView?.getAccountNumber()).thenReturn("*********")
        `when`(mView?.getSelectedAccountType()).thenReturn(null)
        `when`(mView?.getBranchCode()).thenReturn("123456")
        val result = mPresenter?.getRequestViewModel()
        Assert.assertNotNull(result)
        Assert.assertEquals(1, result?.size)
        val cdvRequest = result?.get(0)
        Assert.assertEquals("*********", cdvRequest?.accountNumber)
        Assert.assertNull(cdvRequest?.accountType)
        Assert.assertEquals(123456, cdvRequest?.cdvSortCode)
        Assert.assertEquals(InsuranceConstants.CDV_FUNCTION_VALUE, cdvRequest?.function)
    }

    @Test
    fun getRequestViewModel_cdvSortCode_shouldHandleBranchCodeConversionAndDefault() {
        // Case 1: branchCode is a valid string
        Mockito.`when`(mView?.getBranchCode()).thenReturn("123456")
        val result1 = mPresenter?.getRequestViewModel()
        Assert.assertEquals(123456, result1?.get(0)?.cdvSortCode)

        // Case 2: branchCode is null, should default to Constants.ZERO
        Mockito.`when`(mView?.getBranchCode()).thenReturn(null)
        val result2 = mPresenter?.getRequestViewModel()
        Assert.assertEquals(Constants.ZERO, result2?.get(0)?.cdvSortCode)
    }

    @Test
    fun navigateToBankingDetailCloseViewTest() {
        // Test Line 422: view?.close() call
        `when`(mNavigationRouter!!.navigateTo(ArgumentMatchers.any()))
            .thenReturn(true)

        mPresenter?.navigateToBankingDetail()

        // Verify that navigation is called and view is closed
        verify(mNavigationRouter)?.navigateTo(any())
        verify(mView)?.close()
    }

    @Test
    fun checkIfHavingMultipleBranchTrueTest() {
        // Test Line 447: bank list validation with universal code matching - true case
        val bankDataModel1 = BankDataModel()
        bankDataModel1.universalCode = "123"
        val bankDataModel2 = BankDataModel()
        bankDataModel2.universalCode = "456"
        val bankList = listOf(bankDataModel1, bankDataModel2)

        `when`(mGetAccountTypeList?.execute()).thenReturn(Observable.just(iSelectedViewModel))
        `when`(mGetOverviewUseCase?.execute()).thenReturn(
            Observable.just(
                CachableValue(
                    DomainOverview(),
                    false,
                    false
                )
            )
        )
        `when`(mBankListUseCase?.execute()).thenReturn(Observable.just(bankList))

        mPresenter?.fetchNedbankAccountAndType()

        // Test the method directly
        val result = mPresenter?.checkIfHavingMultipleBranch("123")
        Assert.assertTrue(result == true)
    }

    @Test
    fun checkIfHavingMultipleBranchFalseTest() {
        // Test Line 447: bank list validation with universal code matching - false case
        val bankDataModel1 = BankDataModel()
        bankDataModel1.universalCode = "123"
        val bankDataModel2 = BankDataModel()
        bankDataModel2.universalCode = "456"
        val bankList = listOf(bankDataModel1, bankDataModel2)

        `when`(mGetAccountTypeList?.execute()).thenReturn(Observable.just(iSelectedViewModel))
        `when`(mGetOverviewUseCase?.execute()).thenReturn(
            Observable.just(
                CachableValue(
                    DomainOverview(),
                    false,
                    false
                )
            )
        )
        `when`(mBankListUseCase?.execute()).thenReturn(Observable.just(bankList))

        mPresenter?.fetchNedbankAccountAndType()

        // Test the method directly
        val result = mPresenter?.checkIfHavingMultipleBranch("999") // Non-existing code
        Assert.assertFalse(result == true)
    }

    @Test
    fun getAlreadySelectedAccountTypeFoundTest() {
        // Test Lines 451-452: account type list filtering by description - found case
        val accountTypeList = ArrayList<ISelectedViewModel>()
        val model1 = InsuranceCodeDescriptionViewModel()
        model1.code = "SA"
        model1.description = "Savings Account"
        val model2 = InsuranceCodeDescriptionViewModel()
        model2.code = "CA"
        model2.description = "Current Account"
        accountTypeList.add(model1)
        accountTypeList.add(model2)

        `when`(mView?.getAccountTypeList()).thenReturn(accountTypeList)

        val result = mPresenter?.getAlreadySelectedAccountType("Savings Account")

        Assert.assertNotNull(result)
        Assert.assertEquals("SA", result?.code)
        Assert.assertEquals("Savings Account", result?.description)
    }

    @Test
    fun getAlreadySelectedAccountTypeNotFoundTest() {
        // Test Lines 451-452: account type list filtering by description - not found case
        val accountTypeList = ArrayList<ISelectedViewModel>()
        val model1 = InsuranceCodeDescriptionViewModel()
        model1.code = "SA"
        model1.description = "Savings Account"
        accountTypeList.add(model1)

        `when`(mView?.getAccountTypeList()).thenReturn(accountTypeList)

        val result =
            mPresenter?.getAlreadySelectedAccountType("Current Account") // Non-existing description

        Assert.assertNull(result)
    }

    @Test
    fun getAlreadySelectedAccountTypeNullListTest() {
        // Test Lines 451-452: null account type list scenario
        `when`(mView?.getAccountTypeList()).thenReturn(null)

        val result = mPresenter?.getAlreadySelectedAccountType("Savings Account")

        Assert.assertNull(result)
    }

    @Test
    fun getAlreadySelectedAccountType_found_returnsModel() {
        val accountTypeList = ArrayList<ISelectedViewModel>()
        val model1 = InsuranceCodeDescriptionViewModel()
        model1.code = "SA"
        model1.description = "Savings Account"
        val model2 = InsuranceCodeDescriptionViewModel()
        model2.code = "CA"
        model2.description = "Current Account"
        accountTypeList.add(model1)
        accountTypeList.add(model2)

        Mockito.`when`(mView?.getAccountTypeList()).thenReturn(accountTypeList)

        val result = mPresenter?.getAlreadySelectedAccountType("Savings Account")
        Assert.assertNotNull(result)
        Assert.assertEquals("SA", (result as InsuranceCodeDescriptionViewModel).code)
    }

    @Test
    fun getAlreadySelectedAccountType_notFound_returnsNull() {
        val accountTypeList = ArrayList<ISelectedViewModel>()
        val model1 = InsuranceCodeDescriptionViewModel()
        model1.code = "SA"
        model1.description = "Savings Account"
        accountTypeList.add(model1)

        Mockito.`when`(mView?.getAccountTypeList()).thenReturn(accountTypeList)

        val result = mPresenter?.getAlreadySelectedAccountType("Current Account")
        Assert.assertNull(result)
    }

    @Test
    fun getAlreadySelectedAccountType_nullList_returnsNull() {
        Mockito.`when`(mView?.getAccountTypeList()).thenReturn(null)
        val result = mPresenter?.getAlreadySelectedAccountType("Savings Account")
        Assert.assertNull(result)
    }

    @Test
    fun getAlreadySelectedAccountType_nullTypeCode_returnsNull() {
        val accountTypeList = ArrayList<ISelectedViewModel>()
        val model1 = InsuranceCodeDescriptionViewModel()
        model1.code = "SA"
        model1.description = "Savings Account"
        accountTypeList.add(model1)

        Mockito.`when`(mView?.getAccountTypeList()).thenReturn(accountTypeList)

        val result = mPresenter?.getAlreadySelectedAccountType(null)
        Assert.assertNull(result)
    }

    @Test
    fun moveToManagePolicyScreenNullViewTest() {
        // Test Line 460: null view scenario
        mPresenter?.unbind() // Unbind view to make it null

        `when`(mNavigationRouter!!.navigateTo(ArgumentMatchers.any()))
            .thenReturn(true)

        mPresenter?.moveToManagePolicyScreen()

        // Verify that navigation is called but no view interactions occur
        verify(mNavigationRouter)?.navigateTo(any())
        verifyNoMoreInteractions(mView)
    }

    @Test
    fun moveToManagePolicyScreenWithViewTest() {
        // Test Line 460: with view scenario
        `when`(mNavigationRouter!!.navigateTo(ArgumentMatchers.any()))
            .thenReturn(true)

        mPresenter?.moveToManagePolicyScreen()

        // Verify that navigation is called and view is closed
        verify(mNavigationRouter)?.navigateTo(any())
        verify(mView)?.close()
    }

    @Test
    fun showAPIErrorDirectCallTest() {
        // Test Line 264: direct showAPIError() call
        mPresenter?.showAPIError()

        // Verify that progress bar is hidden and API error is shown
        verify(mView)?.showProgressBar(false)
        verify(mView)?.showAPIError()
    }

    @Test
    fun showAPIError_shouldHideProgressBarAndShowAPIError_whenViewIsNotNull() {
        mPresenter?.showAPIError()
        verify(mView)?.showProgressBar(false)
        verify(mView)?.showAPIError()
    }

    @Test
    fun showAPIError_shouldNotCrash_whenViewIsNull() {
        mPresenter?.unbind() // This sets view to null
        mPresenter?.showAPIError()
        verifyNoMoreInteractions(mView)
    }

    @Test
    fun fetchNedbankAccountAndTypeHandleAccountDetailsTest() {
        // Test Line 237: setAccountTypeList call in handleAccountDetails
        val accountTypeList = ArrayList<ISelectedViewModel>()
        val model = InsuranceCodeDescriptionViewModel()
        model.code = "SA"
        model.description = "Savings Account"
        accountTypeList.add(model)

        val overview = DomainOverview()
        val accountsOverviewList = ArrayList<AccountsOverview>()
        val everydayBankingOverview = AccountsOverview()
        everydayBankingOverview.overviewType = OverviewType.EVERYDAY_BANKING
        val insuranceAccountList = ArrayList<CoreAccountViewModel>()
        val account1 = CoreAccountViewModel()
        account1.accountNumber = "*********"
        insuranceAccountList.add(account1)
        everydayBankingOverview.insuranceAccountList = insuranceAccountList
        accountsOverviewList.add(everydayBankingOverview)
        overview.accountsOverviews = accountsOverviewList

        val bankList = ArrayList<BankDataModel>()
        val bankDataModel = BankDataModel()
        bankDataModel.universalCode = "123"
        bankList.add(bankDataModel)

        `when`(mGetAccountTypeList?.execute()).thenReturn(Observable.just(accountTypeList))
        `when`(mGetOverviewUseCase?.execute()).thenReturn(
            Observable.just(
                CachableValue(
                    overview,
                    false,
                    false
                )
            )
        )
        `when`(mBankListUseCase?.execute()).thenReturn(Observable.just(bankList))

        mPresenter?.fetchNedbankAccountAndType()

        // Verify that both setAccountTypeList and setNedbankAccount are called
        verify(mView)?.setAccountTypeList(accountTypeList)
        verify(mView)?.setNedbankAccount(insuranceAccountList)
    }

    @Test
    fun navigateToBankingListScreenNullViewTest() {
        // Test navigation with null view scenario
        mPresenter?.unbind() // Unbind view to make it null

        val bankViewModel = BankViewModel()
        bankViewModel.bankCode = "abc"
        bankViewModel.bankName = "abc"
        val params: MutableMap<String, Any> = HashMap()
        params[InsuranceConstants.BundleKeys.SELECTED_BANK] = bankViewModel
        val navigationResult = NavigationResult(true, params)
        `when`(mNavigationRouter!!.navigateWithResult(ArgumentMatchers.any()))
            .thenReturn(Observable.just(navigationResult))

        mPresenter?.navigateToBankingListScreen()

        // Verify that navigation is called but no view interactions occur
        verify(mNavigationRouter)?.navigateWithResult(any())
        verifyNoMoreInteractions(mView)
    }

    @Test
    fun navigateToBranchListScreenNullViewTest() {
        // Test navigation with null view scenario
        mPresenter?.unbind() // Unbind view to make it null

        val branchViewModel = BankBranchViewModel()
        branchViewModel.branchCode = "abc"
        branchViewModel.branchName = "abc"
        val params: MutableMap<String, Any> = HashMap()
        params[InsuranceConstants.BundleKeys.SELECTED_BRANCH_MODEL] = branchViewModel
        val navigationResult = NavigationResult(true, params)
        `when`(mNavigationRouter!!.navigateWithResult(ArgumentMatchers.any()))
            .thenReturn(Observable.just(navigationResult))

        mPresenter?.navigateToBranchListScreen(ArrayList<BankBranchViewModel>())

        // Verify that navigation is called but no view interactions occur
        verify(mNavigationRouter)?.navigateWithResult(any())
        verifyNoMoreInteractions(mView)
    }

    @Test
    fun verifyAccountNo_shouldNotCrash_whenViewIsNull() {
        mPresenter?.unbind() // This sets view to null
        // Simulate response from mCDVRequestUseCase and mCDVDataToViewMapper
        val responseDataModels: List<CDVResponseDataModel?> = listOf()
        `when`(mCDVRequestUseCase?.execute(Mockito.any()))
            .thenReturn(Observable.just(responseDataModels))
        `when`(mCDVDataToViewMapper?.mapResponseDataToViewModel(responseDataModels))
            .thenReturn(listOf())
        mPresenter?.verifyAccountNo()
        verifyNoMoreInteractions(mView)
    }

    @Test
    fun verifyAccountNo_shouldCallViewMethods_whenViewIsNotNull() {
        // Simulate response from mCDVRequestUseCase and mCDVDataToViewMapper
        val responseDataModels: List<CDVResponseDataModel?> = listOf()
        `when`(mCDVRequestUseCase?.execute(Mockito.any()))
            .thenReturn(Observable.just(responseDataModels))
        `when`(mCDVDataToViewMapper?.mapResponseDataToViewModel(responseDataModels))
            .thenReturn(listOf())
        mPresenter?.verifyAccountNo()
        // Add your expected view interactions here, e.g.:
        verify(mView)?.showProgressBar(true)
        verify(mView)?.showProgressBar(false)
    }

    @Test
    fun verifyAccountNo_shouldCallMoveToNext_whenPassedYIsYes() {
        // Arrange
        val cdvResponseViewModel = CDVResponseViewModel()
        cdvResponseViewModel.passedY = Constants.CODE_FOR_YES
        val cdvResponseViewModelList = listOf(cdvResponseViewModel)

        `when`(mCDVRequestUseCase?.execute(Mockito.any()))
            .thenReturn(Observable.just(listOf<CDVResponseDataModel>()))
        `when`(mCDVDataToViewMapper?.mapResponseDataToViewModel(Mockito.anyList()))
            .thenReturn(cdvResponseViewModelList)

        mPresenter?.verifyAccountNo()
        verify(mView)?.moveToNext()
    }

    @Test
    fun verifyAccountNo_shouldNotCallMoveToNext_whenPassedYIsNotYes() {
        val cdvResponseViewModel = CDVResponseViewModel()
        cdvResponseViewModel.passedY = "N"
        val cdvResponseViewModelList = listOf(cdvResponseViewModel)

        `when`(mCDVRequestUseCase?.execute(Mockito.any()))
            .thenReturn(Observable.just(listOf<CDVResponseDataModel>()))
        `when`(mCDVDataToViewMapper?.mapResponseDataToViewModel(Mockito.anyList()))
            .thenReturn(cdvResponseViewModelList)

        mPresenter?.verifyAccountNo()

        verify(mView, Mockito.never())?.moveToNext()
    }

    @Test
    fun verifyAccountNo_shouldCallShowAccountAPIError_onError() {
        `when`(mCDVRequestUseCase?.execute(Mockito.any()))
            .thenReturn(Observable.error(Throwable("API error")))

        mPresenter!!.verifyAccountNo()

        verify(mView)?.showAccountAPIError()
    }

    @Test
    fun handleAccountTypeClick_shouldNotExecuteBlock_whenNavigationResultIsNotOk() {
        val navigationResult = NavigationResult(false, emptyMap())
        `when`(mNavigationRouter?.navigateWithResult(any())).thenReturn(Observable.just(navigationResult))
        mPresenter!!.handleAccountTypeClick()
        verify(mView, never())?.setAccountTypeData(any())
    }

    @Test
    fun handleAccountTypeClick_shouldExecuteBlock_whenNavigationResultIsOk() {
        val model = InsuranceCodeDescriptionViewModel().apply {
            code = "SA"
            description = "Savings Account"
        }
        val params: MutableMap<String, Any> = HashMap()
        params[InsuranceConstants.ParamKeys.PARAM_SELECTED_TYPE_MODEL] = model
        val navigationResult = NavigationResult(true, params)
        `when`(mNavigationRouter?.navigateWithResult(any())).thenReturn(Observable.just(navigationResult))
        mPresenter!!.handleAccountTypeClick()
        verify(mView)?.setAccountTypeData(model)
    }

    @Test
    fun navigateToBankingListScreen_shouldHandleNavigationResultNotOk() {
        val navigationResult = NavigationResult(false, HashMap())
        `when`(mNavigationRouter?.navigateWithResult(Mockito.any()))
            .thenReturn(Observable.just(navigationResult))

        mPresenter!!.navigateToBankingListScreen()

        // Assert
        // No view interaction should occur
        verifyNoMoreInteractions(mView)
    }

    @Test
    fun navigateToBankingListScreen_shouldHandleNullView() {
        val navigationResult = NavigationResult(true, HashMap())
        `when`(mNavigationRouter?.navigateWithResult(Mockito.any()))
            .thenReturn(Observable.just(navigationResult))
        mPresenter!!.unbind() // sets view to null


        mPresenter.navigateToBankingListScreen()

        verifyNoMoreInteractions(mView)
    }

    @Test
    fun navigateToBankingListScreen_shouldExecuteBlock_whenNavigationResultIsOkAndViewIsNotNull() {
        val navigationResult = NavigationResult(true, HashMap())
        Mockito.`when`(mNavigationRouter?.navigateWithResult(Mockito.any()))
            .thenReturn(Observable.just(navigationResult))

        mPresenter!!.navigateToBankingListScreen()

        // Assert
        // Add your expected view interactions here
        // e.g. verify(view)?.setSelectedBank(any())
    }

}