package za.co.nedbank.services.insurance.view.vvap_policy_admin.banking

import io.reactivex.Observable
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.Mockito.any
import org.mockito.Mockito.eq
import org.mockito.Mockito.isNull
import org.mockito.Mockito.verify
import org.mockito.junit.MockitoJUnitRunner
import za.co.nedbank.core.errors.Error
import za.co.nedbank.core.errors.ErrorHandler
import za.co.nedbank.core.navigation.NavigationRouter
import za.co.nedbank.core.navigation.NavigationTarget
import za.co.nedbank.core.tracking.Analytics
import za.co.nedbank.core.tracking.InsuranceTrackingEvent
import za.co.nedbank.core.tracking.TrackingEvent
import za.co.nedbank.core.tracking.adobe.AdobeContextData
import za.co.nedbank.services.insurance.domain.data.request.vvaps.claim.policy_detail.PolicyDetailRequestDataModel
import za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.PolicyDetailResponseDataModel
import za.co.nedbank.services.insurance.domain.usecase.personal_lines.PLPolicyDetailUseCase
import za.co.nedbank.services.insurance.domain.usecase.vvap.claim.VVAPPolicyDetailUseCase
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants
import za.co.nedbank.services.insurance.view.other.mapper.vvaps.claim.PolicyDetailRequestViewToDataMapper
import za.co.nedbank.services.insurance.view.other.mapper.vvaps.claim.PolicyDetailResponseDataToViewMapper
import za.co.nedbank.services.insurance.view.other.model.request.vvaps.claim.policy_detail.PolicyDetailRequestViewModel
import za.co.nedbank.services.insurance.view.other.model.response.dashboard.retrieve_policy.AccountViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.*
import za.co.nedbank.services.insurance.view.vvap.policy_admin.banking.banking_detail.PolicyAdminBankingDetailsPresenter
import za.co.nedbank.services.insurance.view.vvap.policy_admin.banking.banking_detail.PolicyAdminBankingDetailsView
import za.co.nedbank.services.insurance.view.vvap.policy_admin.other.helper.PolicyFlowFlags

@RunWith(MockitoJUnitRunner.Silent::class)
class PolicyAdminBankingDetailsPresenterTest {

    @Mock
    private val policyDetailUseCase: VVAPPolicyDetailUseCase? = null

    @Mock
    private val requestViewToDataMapper: PolicyDetailRequestViewToDataMapper? = null

    @Mock
    private val plPolicyDetailUseCase: PLPolicyDetailUseCase? = null

    @Mock
    private val responseDataToViewMapper: PolicyDetailResponseDataToViewMapper? = null

    @Mock
    private val mNavigationRouter: NavigationRouter? = null

    @Mock
    private val mPolicyDetailResponseDataModel: PolicyDetailResponseDataModel? =
        null

    @Mock
    private val mPolicyDetailResponseViewModel: PolicyDetailResponseViewModel? = null

    @Mock
    private val mPolicyDetailRequestViewModel: PolicyDetailRequestViewModel? = null

    @Mock
    private val mPolicyDetailRequestDataModel: PolicyDetailRequestDataModel? = null

    @Mock
    var mView: PolicyAdminBankingDetailsView? = null

    @InjectMocks
    private val mPresenter: PolicyAdminBankingDetailsPresenter? = null

    @Mock
    private val mErrorHandler: ErrorHandler? = null

    @Mock
    private val mThrowable: Throwable? = null

    @Mock
    private val mError: Error? = null

    @Mock
    private val mAnalytics: Analytics? = null

    @Before
    @Throws(Exception::class)
    fun setUp() {
        mPresenter!!.bind(mView)
    }

    @Test
    fun handlePolicyDetailResponseTest() {
        Mockito.doReturn(getPolicyDetailResponseViewModel(true)).`when`(responseDataToViewMapper)!!
            .map(mPolicyDetailResponseDataModel)
        mPresenter!!.handlePolicyDetailResponse(mPolicyDetailResponseDataModel!!)
        Mockito.verify(mView)!!.setBankingDetailData(Mockito.any())
    }

    @Test
    fun handlePolicyDetailResponseMapperNullTest() {
        Mockito.doReturn(getPolicyDetailResponseViewModel(false)).`when`(responseDataToViewMapper)!!
            .map(mPolicyDetailResponseDataModel)
        mPresenter!!.handlePolicyDetailResponse(mPolicyDetailResponseDataModel!!)
        Mockito.verify(mView)!!.showAPIError()
    }

    @Test
    fun getPolicyDetailsGapFlowTest() {
        Mockito.`when`(mErrorHandler?.getErrorMessage(mThrowable)).thenReturn(mError)
        Mockito.`when`(
            requestViewToDataMapper!!.map(
                ArgumentMatchers.any(
                    PolicyDetailRequestViewModel::class.java
                )
            )
        ).thenReturn(mPolicyDetailRequestDataModel)
        Mockito.`when`(policyDetailUseCase!!.execute(mPolicyDetailRequestDataModel))
            .thenReturn(Observable.error(mThrowable))

        mPresenter!!.getPolicyDetails(
            PolicyFlowFlags(
                "123456", true, false, false, false, false, false, false
            )
        )

        Mockito.verify(mView, Mockito.atLeastOnce())!!.showProgressBar(false)
        Mockito.verify(mView, Mockito.atLeastOnce())!!.showAPIError()
    }

    @Test
    fun getPolicyDetailsTLCFlowTest() {
        Mockito.`when`(mErrorHandler?.getErrorMessage(mThrowable)).thenReturn(mError)
        Mockito.`when`(
            requestViewToDataMapper!!.map(
                ArgumentMatchers.any(
                    PolicyDetailRequestViewModel::class.java
                )
            )
        ).thenReturn(mPolicyDetailRequestDataModel)
        Mockito.`when`(policyDetailUseCase!!.execute(mPolicyDetailRequestDataModel))
            .thenReturn(Observable.error(mThrowable))

        mPresenter!!.getPolicyDetails(
            PolicyFlowFlags(
                "123456", false, true, false, false, false, false, false
            )
        )

        Mockito.verify(mView, Mockito.atLeastOnce())!!.showProgressBar(false)
        Mockito.verify(mView, Mockito.atLeastOnce())!!.showAPIError()
    }

    @Test
    fun getPolicyDetailsForPLPolicyTest() {
        // Setup
        val flags = PolicyFlowFlags("123456", false, false, false, false, false, false, true)

        Mockito.`when`(
            requestViewToDataMapper!!.map(
                ArgumentMatchers.any(
                    PolicyDetailRequestViewModel::class.java
                )
            )
        )
            .thenReturn(mPolicyDetailRequestDataModel)
        Mockito.`when`(plPolicyDetailUseCase!!.execute(mPolicyDetailRequestDataModel))
            .thenReturn(Observable.just(mPolicyDetailResponseDataModel))
        Mockito.`when`(responseDataToViewMapper!!.map(mPolicyDetailResponseDataModel))
            .thenReturn(getPolicyDetailResponseViewModel(true))

        // Execute
        mPresenter!!.getPolicyDetails(flags)

        // Verify
        Mockito.verify(mView)!!.showProgressBar(true)
        Mockito.verify(plPolicyDetailUseCase)!!.execute(mPolicyDetailRequestDataModel)
        Mockito.verify(mView)!!.setBankingDetailData(Mockito.any())
        Mockito.verify(mView)!!.showProgressBar(false)
    }

    @Test
    fun getPolicyDetailsForPLPolicyErrorTest() {
        // Setup
        val flags = PolicyFlowFlags("123456", false, false, false, false, false, false, true)

        Mockito.`when`(
            requestViewToDataMapper!!.map(
                ArgumentMatchers.any(
                    PolicyDetailRequestViewModel::class.java
                )
            )
        )
            .thenReturn(mPolicyDetailRequestDataModel)
        Mockito.`when`(plPolicyDetailUseCase!!.execute(mPolicyDetailRequestDataModel))
            .thenReturn(Observable.just(mPolicyDetailResponseDataModel))
        Mockito.`when`(responseDataToViewMapper!!.map(mPolicyDetailResponseDataModel))
            .thenReturn(getPolicyDetailResponseViewModel(false))

        // Execute
        mPresenter!!.getPolicyDetails(flags)

        // Verify
        Mockito.verify(mView)!!.showAPIError()
    }

    @Test
    fun getPolicyDetailsForDefaultFlowTest() {
        testVVAPPolicyFlow(
            PolicyFlowFlags(
                "123456",
                false,
                false,
                false,
                false,
                false,
                false,
                false
            ), InsuranceConstants.ZERO_STRING
        )
    }

//    @Test
//    fun sendEventWithProduct() {
//        val cdata = HashMap<String?, Any?>()
//        val adobeContextData = AdobeContextData(cdata)
//        adobeContextData.setCategoryAndProduct(
//            TrackingEvent.ANALYTICS.INSURENCE, TrackingEvent.ANALYTICS.VVAPS_TITLE
//        )
//        adobeContextData.setProductAccount(TrackingEvent.ANALYTICS.VVAPS_TITLE)
//        adobeContextData.setProductCategory(TrackingEvent.ANALYTICS.INSURENCE)
//        adobeContextData.setSubProduct(InsuranceConstants.ParamKeys.PARAM_VVAPS_SUB_PRODUCT_NAME)
//        mPresenter?.sendEventWithProduct(
//            InsuranceTrackingEvent.EVENT_VVAPS_MANAGE_POLICY_EDIT_BANKING,
//            InsuranceConstants.ParamKeys.PARAM_VVAPS_SUB_PRODUCT_NAME
//        )
//        Mockito.verify(mAnalytics)?.sendEventActionWithMap(
//            InsuranceTrackingEvent.EVENT_VVAPS_MANAGE_POLICY_EDIT_BANKING, cdata
//        )
//    }

    @Test
    fun showAPIErrorTest() {
        mPresenter!!.showAPIError()
        Mockito.verify(mView)?.showProgressBar(false)
        Mockito.verify(mView)?.showAPIError()
    }

    private fun getPolicyDetailResponseViewModel(resultCodeCheck: Boolean): PolicyDetailResponseViewModel {
        val policyDetailResponseViewModel = PolicyDetailResponseViewModel()

        var policyInquiryList = ArrayList<PolicyInquiryViewModel>()
        var resultSetViewModel = ResultSetViewModel()
        if (resultCodeCheck) {
            resultSetViewModel.resultCode = "R00"
        } else resultSetViewModel.resultCode = "R01"

        resultSetViewModel.resultDescription = "Success"

        var policyInquiryViewModel = PolicyInquiryViewModel()

        var policyViewModel = PolicyViewModel()

        var personalVehicleItemSectionViewModel = PersonalVehicleItemSectionViewModel()

        var assignedIdentifierList = ArrayList<AssignedIdentifierViewModel>()
        var coverageList = ArrayList<CoverageViewModel>()
        var policyInterestedPartyReferencesList =
            ArrayList<PolicyInterestedPartyReferenceViewModel>()
        var insuranceAmountItemList = ArrayList<InsuranceAmountItemViewModel>()


        var assignedIdentifierViewModel = AssignedIdentifierViewModel()
        var coverageViewModel = CoverageViewModel()
        var policyInterestedPartyReferenceViewModel = PolicyInterestedPartyReferenceViewModel()
        var typeCodeViewModel = TypeCodeViewModel()
        typeCodeViewModel.value = ""
        var insuranceAmountItemViewModel = InsuranceAmountItemViewModel()
        var contractAmountItemList = ArrayList<ContractAmountItemViewModel>()
        var contractAmountItemViewModel = ContractAmountItemViewModel()
        var itemAmountViewModel = ItemAmountViewModel()
        itemAmountViewModel.value = 12.2f
        contractAmountItemViewModel.itemAmount = itemAmountViewModel
        contractAmountItemViewModel.typeCode = typeCodeViewModel
        contractAmountItemList.add(contractAmountItemViewModel)



        insuranceAmountItemViewModel.contractAmountItem = contractAmountItemList
        insuranceAmountItemViewModel.typeCode = typeCodeViewModel


        var interestedPartyReferences = InterestedPartyReferencesViewModel()
        var interestedPartyPeriod = InterestedPartyPeriodViewModel()

        var organizationReferencesViewModel = OrganizationReferencesViewModel()


        var externalIdentifierViewModel = ExternalIdentifierViewModel()

        externalIdentifierViewModel.id = ""
        externalIdentifierViewModel.typeCode = typeCodeViewModel

        var externalIdentifierList = ArrayList<ExternalIdentifierViewModel>()
        externalIdentifierList.add(externalIdentifierViewModel)

        organizationReferencesViewModel.externalIdentifier = externalIdentifierList

        interestedPartyReferences.organizationReferences = organizationReferencesViewModel
        interestedPartyPeriod.endDate = "22-4-2004"
        interestedPartyPeriod.startDate = "22-4-2004"

        var roleCodeViewModel = RoleCodeViewModel()
        roleCodeViewModel.value = "abc"

        policyInterestedPartyReferenceViewModel.interestedPartyPeriod = interestedPartyPeriod
        policyInterestedPartyReferenceViewModel.interestedPartyReferences =
            interestedPartyReferences
        policyInterestedPartyReferenceViewModel.roleCode = roleCodeViewModel

        assignedIdentifierViewModel.id = ""
        assignedIdentifierViewModel.roleCode = roleCodeViewModel


        var insurerReferences = InsurerReferencesViewModel()
        insurerReferences.externalIdentifier = externalIdentifierList

        var limitViewModel = LimitViewModel()


        var limitAmountViewModel = LimitAmountViewModel()
        limitAmountViewModel.itemAmount = itemAmountViewModel

        limitViewModel.limitAmount = limitAmountViewModel
        limitViewModel.typeCode = typeCodeViewModel

        var limitList = ArrayList<LimitViewModel>()
        limitList.add(limitViewModel)

        var policyReferencesViewModel = PolicyReferencesViewModel()
        policyReferencesViewModel.assignedIdentifier = assignedIdentifierList

        coverageViewModel.effectiveDate = ""
        coverageViewModel.insurerReferences = insurerReferences
        coverageViewModel.limit = limitList
        coverageViewModel.policyReferences = policyReferencesViewModel
        coverageViewModel.typeCode = typeCodeViewModel



        assignedIdentifierList.add(assignedIdentifierViewModel)
        coverageList.add(coverageViewModel)
        policyInterestedPartyReferencesList.add(policyInterestedPartyReferenceViewModel)
        insuranceAmountItemList.add(insuranceAmountItemViewModel)

        personalVehicleItemSectionViewModel.assignedIdentifier = assignedIdentifierList
        personalVehicleItemSectionViewModel.coverage = coverageList
        personalVehicleItemSectionViewModel.insuranceAmountItem = insuranceAmountItemList
        personalVehicleItemSectionViewModel.policyInterestedPartyReferences =
            policyInterestedPartyReferencesList


        var personalVehicleItemSectionList = ArrayList<PersonalVehicleItemSectionViewModel>()
        personalVehicleItemSectionList.add(personalVehicleItemSectionViewModel)

        var policySectionViewModel = PolicySectionViewModel()

        var policyVehicleViewModel = PolicyVehicleViewModel()
        var financeViewModel = FinanceViewModel()
        var financialAmountItemList = ArrayList<FinancialAmountItemViewModel>()
        var amountItemValueList = ArrayList<AmountItemValueViewModel>()
        var amountItemValueViewModel = AmountItemValueViewModel()
        amountItemValueViewModel.itemAmount = itemAmountViewModel
        amountItemValueList.add(amountItemValueViewModel)

        var financialAmountItemViewModel = FinancialAmountItemViewModel()
        financialAmountItemViewModel.typeCode = typeCodeViewModel
        financialAmountItemViewModel.amountItemValue = amountItemValueList
        financialAmountItemList.add(financialAmountItemViewModel)
        financeViewModel.financialAmountItem = financialAmountItemList

        financeViewModel.financeAccountNumberId = "1234"
        financeViewModel.financialAmountItem = financialAmountItemList
        policyVehicleViewModel.finance = financeViewModel

        var policyVehicleList = ArrayList<PolicyVehicleViewModel>()
        policyVehicleList.add(policyVehicleViewModel)
        var policyBillingViewModel = PolicyBillingViewModel()

        var bankAccountList = ArrayList<BankAccountViewModel>()
        var bankAccountViewModel = BankAccountViewModel()
        bankAccountViewModel.accountNumberId = "123"
        bankAccountViewModel.bankName = "abcd"
        bankAccountViewModel.routingNumberId = "123"
        bankAccountViewModel.typeCode = "abc"
        bankAccountList.add(bankAccountViewModel)

        var paymentList = ArrayList<PaymentViewModel>()
        var paymentViewModel = PaymentViewModel()
        var frequencyCode = FrequencyCodeViewModel()
        frequencyCode.value = "abc"
        paymentViewModel.frequencyCode = frequencyCode
        paymentViewModel.typeCode = typeCodeViewModel
        paymentList.add(paymentViewModel)

        policyBillingViewModel.bankAccountList = bankAccountList
        policyBillingViewModel.payment = paymentList


        policySectionViewModel.policyBilling = policyBillingViewModel
        policySectionViewModel.policyVehicle = policyVehicleList


        policyViewModel.personalVehicleItemSection = personalVehicleItemSectionList
        policyViewModel.policySection = policySectionViewModel


        policyInquiryViewModel.policy = policyViewModel
        policyInquiryList.add(policyInquiryViewModel)


        policyDetailResponseViewModel.policyInquiry = policyInquiryList
        policyDetailResponseViewModel.resultSet = resultSetViewModel

        return policyDetailResponseViewModel
    }

    private fun testVVAPPolicyFlow(flags: PolicyFlowFlags, expectedRiskType: String) {
        // Reset mocks
        Mockito.reset(mView, policyDetailUseCase, requestViewToDataMapper, responseDataToViewMapper)

        // Create a test presenter that overrides getRiskSerialNumber
        val testPresenter = PolicyAdminBankingDetailsPresenter(
            mAnalytics!!,
            mNavigationRouter!!,
            policyDetailUseCase!!,
            plPolicyDetailUseCase!!,
            requestViewToDataMapper!!,
            responseDataToViewMapper!!
        )
        testPresenter.bind(mView)

        // Mock the behavior we want for getRiskSerialNumber
        val spyPresenter = Mockito.spy(testPresenter)
        Mockito.doAnswer { invocation ->
            val policyNumber = invocation.arguments[0] as String
            val riskType = invocation.arguments[1] as String
            val callback = invocation.arguments[2] as Function1<String, Unit>

            // Verify correct risk type was passed
            // assertEquals(expectedRiskType, riskType)

            // Call the callback with a test serial number
            callback.invoke("987654")
            null
        }.`when`(spyPresenter).getRiskSerialNumber(
            ArgumentMatchers.anyString(),
            ArgumentMatchers.anyString(),
            ArgumentMatchers.any(),
            ArgumentMatchers.any()
        )

        // Setup remaining mocks
        Mockito.`when`(requestViewToDataMapper.map(ArgumentMatchers.any(PolicyDetailRequestViewModel::class.java)))
            .thenReturn(mPolicyDetailRequestDataModel)
        Mockito.`when`(policyDetailUseCase.execute(mPolicyDetailRequestDataModel))
            .thenReturn(Observable.just(mPolicyDetailResponseDataModel))
        Mockito.`when`(responseDataToViewMapper.map(mPolicyDetailResponseDataModel))
            .thenReturn(getPolicyDetailResponseViewModel(true))

        // Execute
        spyPresenter.getPolicyDetails(flags)

        // Verify
        Mockito.verify(mView)!!.showProgressBar(true)
        Mockito.verify(policyDetailUseCase)!!.execute(mPolicyDetailRequestDataModel)
        Mockito.verify(mView)!!.setBankingDetailData(Mockito.any())
        Mockito.verify(mView)!!.showProgressBar(false)
    }

    @Test
    fun sendEventWithProductForManagePolicyGAP() {
        val cdata = HashMap<String?, Any?>()
        val adobeContextData = AdobeContextData(cdata)
        adobeContextData.setCategoryAndProduct(
            TrackingEvent.ANALYTICS.INSURANCE_PRODUCT_CATEGORY, TrackingEvent.ANALYTICS.VVAPS_TITLE
        )
        adobeContextData.setSubProduct(InsuranceConstants.AnalyticsConstants.SUBPRODUCT_GAP)
        adobeContextData.setProductAccount(TrackingEvent.ANALYTICS.VVAPS_TITLE)
        adobeContextData.setProductCategory(TrackingEvent.ANALYTICS.INSURENCE)
        mPresenter?.sendEventWithProduct(
            InsuranceTrackingEvent.EVENT_MANAGE_POLICY_SAVE_WARRANTY,
            InsuranceConstants.AnalyticsConstants.SUBPRODUCT_GAP,
            TrackingEvent.ANALYTICS.VVAPS_TITLE
        )
        Mockito.verify(mAnalytics)?.sendEventActionWithMap(
            InsuranceTrackingEvent.EVENT_MANAGE_POLICY_SAVE_WARRANTY, cdata
        )
    }

    @Test
    fun sendEventWithProduct_shouldSendEventWithCorrectData() {
        val eventName = "test_event"
        val subProduct = "test_sub_product"
        mPresenter!!.sendEventWithProduct(
            eventName,
            subProduct,
            TrackingEvent.ANALYTICS.VVAPS_TITLE
        )
        verify(mAnalytics)?.sendEventActionWithMap(eq(eventName), any())
    }

    @Test
    fun sendEventWithProduct_shouldHandleNullEventNameAndSubProduct() {
        mPresenter!!.sendEventWithProduct(null, null, TrackingEvent.ANALYTICS.PL_COMBO_TITLE)
        verify(mAnalytics)?.sendEventActionWithMap(isNull(), any())
    }


    @Test
    fun getPolicyDetailsPLFlowFailureTest() {
        val policyNumber = "123456"

        Mockito.`when`(
            requestViewToDataMapper!!.map(
                ArgumentMatchers.any(PolicyDetailRequestViewModel::class.java)
            )
        ).thenReturn(mPolicyDetailRequestDataModel)

        Mockito.`when`(plPolicyDetailUseCase!!.execute(mPolicyDetailRequestDataModel))
            .thenReturn(Observable.error(mThrowable))

        mPresenter!!.getPolicyDetails(
            PolicyFlowFlags(
                policyNumber,
                false,
                false,
                false,
                false,
                false,
                false,
                true
            )
        )

        Mockito.verify(mView, Mockito.atLeastOnce())!!.showProgressBar(true)
        Mockito.verify(mView, Mockito.atLeastOnce())!!.showProgressBar(false)
        Mockito.verify(mView)!!.showAPIError()
    }

    @Test
    fun getPolicyDetailsTlcFlowTest() {
        val policyNumber = "123456"

        // Mock the getRiskSerialNumber method to call the callback
        val spy = Mockito.spy(mPresenter)
        Mockito.doAnswer { invocation ->
            val callback = invocation.getArgument<(String) -> Unit>(2)
            callback.invoke("2")
            null
        }.`when`(spy)?.getRiskSerialNumber(
            ArgumentMatchers.anyString(),
            ArgumentMatchers.anyString(),
            ArgumentMatchers.any(),
            ArgumentMatchers.any()
        )

        Mockito.`when`(
            requestViewToDataMapper!!.map(
                ArgumentMatchers.any(PolicyDetailRequestViewModel::class.java)
            )
        ).thenReturn(mPolicyDetailRequestDataModel)

        Mockito.`when`(policyDetailUseCase!!.execute(mPolicyDetailRequestDataModel))
            .thenReturn(Observable.just(mPolicyDetailResponseDataModel))

        Mockito.doReturn(getPolicyDetailResponseViewModel(true)).`when`(responseDataToViewMapper)!!
            .map(mPolicyDetailResponseDataModel)

        spy!!.getPolicyDetails(
            PolicyFlowFlags(
                policyNumber,
                false,
                true,
                false,
                false,
                false,
                false,
                false
            )
        )

        Mockito.verify(mView, Mockito.atLeastOnce())!!.showProgressBar(true)
        Mockito.verify(mView, Mockito.atLeastOnce())!!.showProgressBar(false)
        Mockito.verify(mView)!!.setBankingDetailData(Mockito.any())
    }

    @Test
    fun getPolicyDetailsDentScratchFlowTest() {
        val policyNumber = "123456"

        // Mock the getRiskSerialNumber method to call the callback
        val spy = Mockito.spy(mPresenter)
        Mockito.doAnswer { invocation ->
            val callback = invocation.getArgument<(String) -> Unit>(2)
            callback.invoke("3")
            null
        }.`when`(spy)?.getRiskSerialNumber(
            ArgumentMatchers.anyString(),
            ArgumentMatchers.anyString(),
            ArgumentMatchers.any(),
            ArgumentMatchers.any()
        )

        Mockito.`when`(
            requestViewToDataMapper!!.map(
                ArgumentMatchers.any(PolicyDetailRequestViewModel::class.java)
            )
        ).thenReturn(mPolicyDetailRequestDataModel)

        Mockito.`when`(policyDetailUseCase!!.execute(mPolicyDetailRequestDataModel))
            .thenReturn(Observable.just(mPolicyDetailResponseDataModel))

        Mockito.doReturn(getPolicyDetailResponseViewModel(true)).`when`(responseDataToViewMapper)!!
            .map(mPolicyDetailResponseDataModel)

        spy!!.getPolicyDetails(
            PolicyFlowFlags(
                policyNumber,
                false,
                false,
                true,
                false,
                false,
                false,
                false
            )
        )

        Mockito.verify(mView, Mockito.atLeastOnce())!!.showProgressBar(true)
        Mockito.verify(mView, Mockito.atLeastOnce())!!.showProgressBar(false)
        Mockito.verify(mView)!!.setBankingDetailData(Mockito.any())
    }

    @Test
    fun getPolicyDetailsTyreRimFlowTest() {
        val policyNumber = "123456"

        // Mock the getRiskSerialNumber method to call the callback
        val spy = Mockito.spy(mPresenter)
        Mockito.doAnswer { invocation ->
            val callback = invocation.getArgument<(String) -> Unit>(2)
            callback.invoke("4")
            null
        }.`when`(spy)?.getRiskSerialNumber(
            ArgumentMatchers.anyString(),
            ArgumentMatchers.anyString(),
            ArgumentMatchers.any(),
            ArgumentMatchers.any()
        )

        Mockito.`when`(
            requestViewToDataMapper!!.map(
                ArgumentMatchers.any(PolicyDetailRequestViewModel::class.java)
            )
        ).thenReturn(mPolicyDetailRequestDataModel)

        Mockito.`when`(policyDetailUseCase!!.execute(mPolicyDetailRequestDataModel))
            .thenReturn(Observable.just(mPolicyDetailResponseDataModel))

        Mockito.doReturn(getPolicyDetailResponseViewModel(true)).`when`(responseDataToViewMapper)!!
            .map(mPolicyDetailResponseDataModel)

        spy!!.getPolicyDetails(
            PolicyFlowFlags(
                policyNumber,
                false,
                false,
                false,
                true,
                false,
                false,
                false
            )
        )

        Mockito.verify(mView, Mockito.atLeastOnce())!!.showProgressBar(true)
        Mockito.verify(mView, Mockito.atLeastOnce())!!.showProgressBar(false)
        Mockito.verify(mView)!!.setBankingDetailData(Mockito.any())
    }

    @Test
    fun getPolicyDetailsEssentialFlowTest() {
        val policyNumber = "123456"

        // Mock the getRiskSerialNumber method to call the callback
        val spy = Mockito.spy(mPresenter)
        Mockito.doAnswer { invocation ->
            val callback = invocation.getArgument<(String) -> Unit>(2)
            callback.invoke("5")
            null
        }.`when`(spy)?.getRiskSerialNumber(
            ArgumentMatchers.anyString(),
            ArgumentMatchers.anyString(),
            ArgumentMatchers.any(),
            ArgumentMatchers.any()
        )

        Mockito.`when`(
            requestViewToDataMapper!!.map(
                ArgumentMatchers.any(PolicyDetailRequestViewModel::class.java)
            )
        ).thenReturn(mPolicyDetailRequestDataModel)

        Mockito.`when`(policyDetailUseCase!!.execute(mPolicyDetailRequestDataModel))
            .thenReturn(Observable.just(mPolicyDetailResponseDataModel))

        Mockito.doReturn(getPolicyDetailResponseViewModel(true)).`when`(responseDataToViewMapper)!!
            .map(mPolicyDetailResponseDataModel)

        spy!!.getPolicyDetails(
            PolicyFlowFlags(
                policyNumber,
                false,
                false,
                false,
                false,
                true,
                false,
                false
            )
        )

        Mockito.verify(mView, Mockito.atLeastOnce())!!.showProgressBar(true)
        Mockito.verify(mView, Mockito.atLeastOnce())!!.showProgressBar(false)
        Mockito.verify(mView)!!.setBankingDetailData(Mockito.any())
    }

    @Test
    fun getPolicyDetailsComprehensiveFlowTest() {
        val policyNumber = "123456"

        // Mock the getRiskSerialNumber method to call the callback
        val spy = Mockito.spy(mPresenter)
        Mockito.doAnswer { invocation ->
            val callback = invocation.getArgument<(String) -> Unit>(2)
            callback.invoke("6")
            null
        }.`when`(spy)?.getRiskSerialNumber(
            ArgumentMatchers.anyString(),
            ArgumentMatchers.anyString(),
            ArgumentMatchers.any(),
            ArgumentMatchers.any()
        )

        Mockito.`when`(
            requestViewToDataMapper!!.map(
                ArgumentMatchers.any(PolicyDetailRequestViewModel::class.java)
            )
        ).thenReturn(mPolicyDetailRequestDataModel)

        Mockito.`when`(policyDetailUseCase!!.execute(mPolicyDetailRequestDataModel))
            .thenReturn(Observable.just(mPolicyDetailResponseDataModel))

        Mockito.doReturn(getPolicyDetailResponseViewModel(true)).`when`(responseDataToViewMapper)!!
            .map(mPolicyDetailResponseDataModel)

        spy!!.getPolicyDetails(
            PolicyFlowFlags(
                policyNumber,
                false,
                false,
                false,
                false,
                false,
                true,
                false
            )
        )

        Mockito.verify(mView, Mockito.atLeastOnce())!!.showProgressBar(true)
        Mockito.verify(mView, Mockito.atLeastOnce())!!.showProgressBar(false)
        Mockito.verify(mView)!!.setBankingDetailData(Mockito.any())
    }

    @Test
    fun getPolicyDetailsDefaultFlowTest() {
        val policyNumber = "123456"

        // Mock the getRiskSerialNumber method to call the callback
        val spy = Mockito.spy(mPresenter)
        Mockito.doAnswer { invocation ->
            val callback = invocation.getArgument<(String) -> Unit>(2)
            callback.invoke("0")
            null
        }.`when`(spy)?.getRiskSerialNumber(
            ArgumentMatchers.anyString(),
            ArgumentMatchers.anyString(),
            ArgumentMatchers.any(),
            ArgumentMatchers.any()
        )

        Mockito.`when`(
            requestViewToDataMapper!!.map(
                ArgumentMatchers.any(PolicyDetailRequestViewModel::class.java)
            )
        ).thenReturn(mPolicyDetailRequestDataModel)

        Mockito.`when`(policyDetailUseCase!!.execute(mPolicyDetailRequestDataModel))
            .thenReturn(Observable.just(mPolicyDetailResponseDataModel))

        Mockito.doReturn(getPolicyDetailResponseViewModel(true)).`when`(responseDataToViewMapper)!!
            .map(mPolicyDetailResponseDataModel)

        spy!!.getPolicyDetails(
            PolicyFlowFlags(
                policyNumber,
                false,
                false,
                false,
                false,
                false,
                false,
                false
            )
        )

        Mockito.verify(mView, Mockito.atLeastOnce())!!.showProgressBar(true)
        Mockito.verify(mView, Mockito.atLeastOnce())!!.showProgressBar(false)
        Mockito.verify(mView)!!.setBankingDetailData(Mockito.any())
    }

    @Test
    fun navigateToPolicyClaimsTest() {
        Mockito.`when`(mNavigationRouter!!.navigateTo(ArgumentMatchers.any())).thenReturn(true)

        mPresenter!!.navigateToPolicyClaims()

        Mockito.verify(mNavigationRouter)
            .navigateTo(NavigationTarget.to(NavigationTarget.INSURANCE_POLICY_CLAIMS_SCREEN))
        Mockito.verify(mView)!!.close()
    }

    @Test
    fun showViewProgressBarTest() {
        mPresenter?.showViewProgressBar(true)
        Mockito.verify(mView)?.showProgressBar(true)

        mPresenter?.showViewProgressBar(false)
        Mockito.verify(mView)?.showProgressBar(false)
    }

    @Test
    fun showViewProgressBarNullViewTest() {
        mPresenter?.unbind()
        mPresenter?.showViewProgressBar(true)
        // Should not crash when view is null
    }

    @Test
    fun getRiskSerialNumberErrorCallbackTest() {
        // Test the getRiskSerialNumber method with error case
        val spy = Mockito.spy(mPresenter)
        Mockito.doAnswer { invocation ->
            val errorCallback = invocation.getArgument<() -> Unit>(3)
            errorCallback.invoke()
            null
        }.`when`(spy)?.getRiskSerialNumber(
            ArgumentMatchers.anyString(),
            ArgumentMatchers.anyString(),
            ArgumentMatchers.any(),
            ArgumentMatchers.any()
        )

        val flags = PolicyFlowFlags(
            "123456", true, false, false, false, false, false, false
        )

        spy!!.getPolicyDetails(flags)

        Mockito.verify(mView)!!.showAPIError()
    }

    @Test
    fun sendEventWithProductNullParametersTest() {
        val cdata = HashMap<String?, Any?>()
        val adobeContextData = AdobeContextData(cdata)
        adobeContextData.setCategoryAndProduct(
            TrackingEvent.ANALYTICS.INSURANCE_PRODUCT_CATEGORY, TrackingEvent.ANALYTICS.VVAPS_TITLE
        )
        adobeContextData.setSubProduct(null)
        adobeContextData.setProductAccount(TrackingEvent.ANALYTICS.VVAPS_TITLE)
        adobeContextData.setProductCategory(TrackingEvent.ANALYTICS.INSURENCE)
        mPresenter?.sendEventWithProduct(null, null, "VVAPS")
        Mockito.verify(mAnalytics)?.sendEventActionWithMap(
            null, cdata
        )
    }

    @Test
    fun navigateToPolicyClaimsWithViewNullTest() {
        mPresenter!!.unbind()
        Mockito.`when`(mNavigationRouter!!.navigateTo(ArgumentMatchers.any())).thenReturn(true)

        mPresenter!!.navigateToPolicyClaims()

        Mockito.verify(mNavigationRouter)
            .navigateTo(NavigationTarget.to(NavigationTarget.INSURANCE_POLICY_CLAIMS_SCREEN))
        // Should not crash when view is null
    }

    @Test
    fun editBankingDetailsFullParametersTest() {
        Mockito.`when`(mNavigationRouter!!.navigateTo(ArgumentMatchers.any())).thenReturn(true)

        val bankAccountViewModel = BankAccountViewModel()
        bankAccountViewModel.accountNumberId = "123"
        bankAccountViewModel.bankName = "Test Bank"

        val accountViewModel = AccountViewModel()

        mPresenter!!.editBankingDetails(
            "123456", bankAccountViewModel, accountViewModel, "Test Product", true,true
        )

        Mockito.verify(mNavigationRouter)
            .navigateTo(ArgumentMatchers.any(NavigationTarget::class.java))
    }

    @Test
    fun editBankingDetailsNullProductNameTest() {
        Mockito.`when`(mNavigationRouter!!.navigateTo(ArgumentMatchers.any())).thenReturn(true)

        val bankAccountViewModel = BankAccountViewModel()
        val accountViewModel = AccountViewModel()

        mPresenter!!.editBankingDetails(
            "123456", bankAccountViewModel, accountViewModel, null, false, false
        )

        Mockito.verify(mNavigationRouter)
            .navigateTo(ArgumentMatchers.any(NavigationTarget::class.java))
    }

    @Test
    fun handlePolicyDetailResponseWithNullBankingDetailsTest() {
        val policyDetailResponseViewModel = PolicyDetailResponseViewModel()

        var policyInquiryList = ArrayList<PolicyInquiryViewModel>()
        var resultSetViewModel = ResultSetViewModel()
        resultSetViewModel.resultCode = "R00"
        resultSetViewModel.resultDescription = "Success"

        var policyInquiryViewModel = PolicyInquiryViewModel()
        var policyViewModel = PolicyViewModel()
        var policySectionViewModel = PolicySectionViewModel()

        // Create a valid response but with null banking details
        policyViewModel.policySection = policySectionViewModel
        policyInquiryViewModel.policy = policyViewModel
        policyInquiryList.add(policyInquiryViewModel)

        policyDetailResponseViewModel.policyInquiry = policyInquiryList
        policyDetailResponseViewModel.resultSet = resultSetViewModel

        Mockito.doReturn(policyDetailResponseViewModel).`when`(responseDataToViewMapper)!!
            .map(mPolicyDetailResponseDataModel)

        mPresenter!!.handlePolicyDetailResponse(mPolicyDetailResponseDataModel!!)

        // The method should handle this gracefully without setting banking details
    }

    @Test
    fun editBankingDetailsNullBankAccountTest() {
        Mockito.`when`(mNavigationRouter!!.navigateTo(ArgumentMatchers.any())).thenReturn(true)

        val accountViewModel = AccountViewModel()

        // Testing with null selectedBankingDetail parameter
        mPresenter!!.editBankingDetails(
            "123456", null, accountViewModel, "Test Product", false,false
        )

        Mockito.verify(mNavigationRouter)
            .navigateTo(ArgumentMatchers.any(NavigationTarget::class.java))
    }

    @Test
    fun editBankingDetailsTest() {
        Mockito.`when`(mNavigationRouter!!.navigateTo(ArgumentMatchers.any())).thenReturn(true)

        val bankAccountViewModel = BankAccountViewModel()
        bankAccountViewModel.accountNumberId = "123"
        bankAccountViewModel.bankName = "Test Bank"

        val accountViewModel = AccountViewModel()

        mPresenter!!.editBankingDetails(
            "123456", bankAccountViewModel, accountViewModel, "Test Product", true,true
        )

        Mockito.verify(mNavigationRouter)
            .navigateTo(ArgumentMatchers.any(NavigationTarget::class.java))
    }

    @Test
    fun editBankingDetailsNullAccountViewModelTest() {
        Mockito.`when`(mNavigationRouter!!.navigateTo(ArgumentMatchers.any())).thenReturn(true)

        val bankAccountViewModel = BankAccountViewModel()
        val accountViewModel = AccountViewModel()

        // Testing with null accountViewModel parameter
        mPresenter!!.editBankingDetails(
            "123456", bankAccountViewModel, accountViewModel, "Test Product", true,true
        )

        Mockito.verify(mNavigationRouter)
            .navigateTo(ArgumentMatchers.any(NavigationTarget::class.java))
    }

    @Test
    fun sendEventWithProductEmptyStringsTest() {
        val cdata = HashMap<String?, Any?>()
        val adobeContextData = AdobeContextData(cdata)
        adobeContextData.setCategoryAndProduct(
            TrackingEvent.ANALYTICS.INSURANCE_PRODUCT_CATEGORY, TrackingEvent.ANALYTICS.VVAPS_TITLE
        )
        adobeContextData.setSubProduct("")
        adobeContextData.setProductAccount(TrackingEvent.ANALYTICS.VVAPS_TITLE)
        adobeContextData.setProductCategory(TrackingEvent.ANALYTICS.INSURENCE)

        // Test with empty strings instead of nulls
        mPresenter?.sendEventWithProduct("", "", "VVAPS")

        Mockito.verify(mAnalytics)?.sendEventActionWithMap(
            "", cdata
        )
    }

    @Test
    fun sendEventWithProductTest() {
        val cdata = HashMap<String, Any>()
        val adobeContextData = AdobeContextData(cdata)
        adobeContextData.setCategoryAndProduct(
            TrackingEvent.ANALYTICS.INSURANCE_PRODUCT_CATEGORY, TrackingEvent.ANALYTICS.VVAPS_TITLE
        )
        adobeContextData.setProductCategory(TrackingEvent.ANALYTICS.INSURENCE)
        adobeContextData.setProductAccount(TrackingEvent.ANALYTICS.VVAPS_TITLE)
        adobeContextData.setSubProduct("Test SubProduct")

        mPresenter?.sendEventWithProduct("Test Event", "Test SubProduct", "VVAPS")

        Mockito.verify(mAnalytics)?.sendEventActionWithMap(
            "Test Event", cdata
        )
    }
}