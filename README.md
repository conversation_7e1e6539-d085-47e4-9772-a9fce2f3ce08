## NedBank Android [![CircleCI](https://circleci.com/gh/greenbank60days/banking-app-droid.svg?style=svg&circle-token=****************************************)](https://circleci.com/gh/greenbank60days/banking-app-droid)

#### Environment:
- Android Studio 3.0
- Java 1.8 or higher
- Minimal supported Android version - API 17 (4.2)

#### Architecture
- MVP with clean code

#### Guidelines
###### Adding new library to project
If adding new library, please add it's version to top build.gradle file, into **ext** section

>
>     // Define versions in a single place
>     ext {
>         // some libraries
>         rxlifecycleVersion = '2.1.0'
>         // more libraries
>     }

Then in selected build.gradle file, you can reference it

>		compile "com.trello.rxlifecycle2:rxlifecycle:$rootProject.rxlifecycleVersion"
>

In this way, we'll use the same version of library, across all modules.

#### Libraries:
- [Retrofit2](https://github.com/square/retrofit/) - network client
- [<PERSON><PERSON>](https://github.com/square/moshi) - json library
- [Retrofit2 Moshi Converter](https://github.com/square/retrofit/tree/master/retrofit-converters/moshi) - moshi support in retrofit2
- [Picasso](https://github.com/square/picasso) - image downloading/caching
- [RxJava2](https://github.com/ReactiveX/RxJava) - reactive programming
- [RxBinding2](https://github.com/JakeWharton/RxBinding) - Android UI binding for RxJava
- [RxLifecycle2](https://github.com/trello/RxLifecycle) - lifecycle handling in RxJava apps
- [RxPermissions](https://github.com/tbruyelle/RxPermissions) - library for checking permissions
- [RxBroadcast](https://github.com/cantrowitz/RxBroadcast) - library for listening to broadcast messages
- [Dagger2](https://github.com/google/dagger) - dependency injection

#### Testing:
- [Espresso](https://google.github.io/android-testing-support-library/docs/espresso/)
- [Mockito](https://github.com/mockito/mockito)
- [Junit](http://junit.org/junit4/)

# React Native Insurance Dashboard POC

## Overview
This is a Proof of Concept (POC) for moving the Nedbank Insurance section to React Native. The goal is to demonstrate how a single React Native codebase can replace the current dual Android/iOS native implementations.

## What's Implemented

### 1. Dashboard Integration
- Added a new "React Native" option to the main dashboard
- Uses the same insurance icon as the existing insurance option
- Appears alongside existing options like "Offers for you", "Applications", "Insure", etc.

### 2. Navigation Flow
- **HomeWidget.REACT_NATIVE_INSURANCE** - New widget enum entry
- **ACTION_REACT_NATIVE_INSURANCE** - New action constant (ID: 20)
- **NavigationTarget.REACT_NATIVE_INSURANCE_DASHBOARD_SCREEN** - New navigation target
- **ReactNativeInsuranceActivity** - Host activity for the React Native component

### 3. UI Components
For this POC, we've created a native Android activity that simulates the React Native interface:
- **My Insurance** section with action cards
- **Manage policies and claims** button
- **Complete applications** button  
- **Emergency services** button
- **Get Cover** section with product options
- **Funeral Cover** and **Life Cover** buttons

## Files Modified/Created

### Core Changes
- `core/src/main/java/za/co/nedbank/core/dashboard/HomeWidget.java` - Added REACT_NATIVE_INSURANCE widget
- `core/src/main/java/za/co/nedbank/core/navigation/NavigationTarget.java` - Added navigation target
- `core/src/main/res/values/strings.xml` - Added "React Native" string resource

### App Changes
- `app/src/main/java/za/co/nedbank/ui/view/home/<USER>/OverviewFragment.java` - Added action handling
- `app/src/main/java/za/co/nedbank/ui/view/home/<USER>/OverviewPresenter.java` - Added navigation method
- `app/src/main/java/za/co/nedbank/ui/AppNavigator.java` - Registered navigation target
- `app/src/main/AndroidManifest.xml` - Added ReactNativeInsuranceActivity

### New Files
- `app/src/main/java/za/co/nedbank/ui/view/reactnative/ReactNativeInsuranceActivity.java` - POC activity
- `react-native/` - React Native project structure (for future implementation)

## How to Test

1. **Build and run the app**
2. **Navigate to the main dashboard** (after login)
3. **Look for the "React Native" option** with the insurance icon
4. **Tap the React Native option**
5. **Verify navigation** to the React Native Insurance Dashboard POC
6. **Test the buttons** - they should show toast messages indicating React Native functionality

## Current POC Limitations

1. **Simulated React Native**: Currently using native Android views to simulate the React Native interface
2. **No actual React Native runtime**: Full React Native integration will be implemented in future phases
3. **Android only**: This POC focuses on Android; iOS implementation will follow
4. **Limited functionality**: Buttons show toast messages rather than actual insurance functionality

## Next Steps for Full Implementation

### Phase 1: React Native Setup
1. Add full React Native dependencies
2. Set up React Native bundle building
3. Configure Metro bundler
4. Implement actual React Native components

### Phase 2: Feature Implementation
1. Implement insurance dashboard in React Native
2. Add navigation between React Native screens
3. Integrate with existing insurance APIs
4. Implement state management (Redux/Context)

### Phase 3: iOS Integration
1. Create iOS React Native host
2. Add iOS navigation integration
3. Test cross-platform functionality
4. Performance optimization

### Phase 4: Production Readiness
1. Error handling and logging
2. Performance monitoring
3. A/B testing framework
4. Gradual rollout strategy

## Benefits of React Native Approach

1. **Single Codebase**: One team can maintain both Android and iOS
2. **Faster Development**: Shared business logic and UI components
3. **Consistent UX**: Identical behavior across platforms
4. **Easier Testing**: Single test suite for both platforms
5. **Faster Feature Delivery**: No need to coordinate between two teams

## Architecture Considerations

- **Bridge Communication**: Native ↔ React Native data exchange
- **Navigation**: Seamless integration with existing native navigation
- **State Management**: Shared state between native and React Native
- **Performance**: Optimize bundle size and runtime performance
- **Debugging**: Development tools and error reporting

## POC Success Criteria ✅

- [x] New dashboard option appears correctly
- [x] Navigation to React Native screen works
- [x] UI resembles current insurance dashboard
- [x] Demonstrates concept feasibility
- [x] No breaking changes to existing functionality

This POC successfully demonstrates the feasibility of moving the insurance section to React Native while maintaining the existing user experience and navigation patterns.
