import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../../navigation/AppNavigator';

import Card from '../../components/common/Card';
import Button from '../../components/common/Button';

type RouteProp = RouteProp<RootStackParamList, 'ThankYou'>;
type NavigationProp = StackNavigationProp<RootStackParamList, 'ThankYou'>;

const ThankYou: React.FC = () => {
  const route = useRoute<RouteProp>();
  const navigation = useNavigation<NavigationProp>();
  const { 
    title = 'Thank you', 
    message = 'Your request has been processed successfully.',
    nextAction = 'Continue'
  } = route.params || {};

  const handlePrimaryAction = () => {
    if (nextAction === 'View Policies') {
      navigation.navigate('PolicyList');
    } else if (nextAction === 'Dashboard') {
      navigation.navigate('InsuranceDashboard');
    } else {
      Alert.alert(
        'React Native POC',
        `This would perform the "${nextAction}" action. React Native provides flexible navigation and action handling.`,
        [
          {
            text: 'Go to Dashboard',
            onPress: () => navigation.navigate('InsuranceDashboard'),
          },
        ]
      );
    }
  };

  const handleSecondaryAction = () => {
    Alert.alert(
      'React Native POC',
      'This would provide additional actions like sharing, downloading, or contacting support.',
      [{ text: 'OK' }]
    );
  };

  const handleGoHome = () => {
    navigation.navigate('InsuranceDashboard');
  };

  const getSuccessIcon = () => {
    if (title.toLowerCase().includes('application')) return '📋';
    if (title.toLowerCase().includes('quote')) return '💰';
    if (title.toLowerCase().includes('policy')) return '🛡️';
    if (title.toLowerCase().includes('claim')) return '✅';
    return '🎉';
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Success Header */}
        <View style={styles.successHeader}>
          <View style={styles.successIcon}>
            <Text style={styles.successIconText}>{getSuccessIcon()}</Text>
          </View>
          <Text style={styles.successTitle}>{title}</Text>
          <Text style={styles.successMessage}>{message}</Text>
        </View>

        {/* Success Details */}
        <Card style={styles.detailsCard} variant="elevated" padding="large" margin="medium">
          <Text style={styles.detailsTitle}>What happens next?</Text>
          
          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>1</Text>
            </View>
            <View style={styles.stepContent}>
              <Text style={styles.stepTitle}>Confirmation</Text>
              <Text style={styles.stepDescription}>
                You'll receive a confirmation email with all the details within the next few minutes.
              </Text>
            </View>
          </View>
          
          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>2</Text>
            </View>
            <View style={styles.stepContent}>
              <Text style={styles.stepTitle}>Processing</Text>
              <Text style={styles.stepDescription}>
                Our team will review and process your request within 2-3 business days.
              </Text>
            </View>
          </View>
          
          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>3</Text>
            </View>
            <View style={styles.stepContent}>
              <Text style={styles.stepTitle}>Updates</Text>
              <Text style={styles.stepDescription}>
                We'll keep you informed of any updates via email and SMS.
              </Text>
            </View>
          </View>
        </Card>

        {/* Quick Actions */}
        <Card style={styles.actionsCard} variant="default" padding="large" margin="medium">
          <Text style={styles.actionsTitle}>Quick Actions</Text>
          
          <View style={styles.actionsList}>
            <View style={styles.actionItem}>
              <Text style={styles.actionIcon}>📱</Text>
              <View style={styles.actionContent}>
                <Text style={styles.actionTitle}>Download our app</Text>
                <Text style={styles.actionDescription}>
                  Get the Nedbank app for easy access to your policies and claims.
                </Text>
              </View>
            </View>
            
            <View style={styles.actionItem}>
              <Text style={styles.actionIcon}>📞</Text>
              <View style={styles.actionContent}>
                <Text style={styles.actionTitle}>Contact support</Text>
                <Text style={styles.actionDescription}>
                  Call 0860 555 111 if you have any questions or need assistance.
                </Text>
              </View>
            </View>
            
            <View style={styles.actionItem}>
              <Text style={styles.actionIcon}>💬</Text>
              <View style={styles.actionContent}>
                <Text style={styles.actionTitle}>Live chat</Text>
                <Text style={styles.actionDescription}>
                  Chat with our support team for immediate assistance.
                </Text>
              </View>
            </View>
          </View>
        </Card>

        {/* Important Information */}
        <Card style={styles.infoCard} variant="outlined" padding="medium" margin="medium">
          <Text style={styles.infoTitle}>Important Information</Text>
          <Text style={styles.infoText}>
            • Keep your reference number safe for future correspondence{'\n'}
            • Check your email for confirmation and next steps{'\n'}
            • You can track progress online or through our app{'\n'}
            • Contact us immediately if any details are incorrect
          </Text>
        </Card>

        {/* Feedback */}
        <Card style={styles.feedbackCard} variant="flat" padding="medium" margin="medium">
          <Text style={styles.feedbackTitle}>How was your experience?</Text>
          <Text style={styles.feedbackText}>
            Your feedback helps us improve our services.
          </Text>
          <View style={styles.ratingContainer}>
            {[1, 2, 3, 4, 5].map((rating) => (
              <Text key={rating} style={styles.ratingStar}>⭐</Text>
            ))}
          </View>
        </Card>

        {/* React Native POC Notice */}
        <Card style={styles.pocNotice} variant="flat" padding="medium" margin="medium">
          <View style={styles.pocContent}>
            <Text style={styles.pocIcon}>🎯</Text>
            <Text style={styles.pocText}>
              React Native POC - Professional success screens with clear next steps and user engagement
            </Text>
          </View>
        </Card>
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.footer}>
        <Button
          title={nextAction}
          onPress={handlePrimaryAction}
          variant="primary"
          fullWidth
          size="large"
          style={styles.primaryButton}
        />
        
        <View style={styles.secondaryButtons}>
          <Button
            title="Share"
            onPress={handleSecondaryAction}
            variant="outline"
            size="medium"
            style={styles.secondaryButton}
          />
          
          <Button
            title="Dashboard"
            onPress={handleGoHome}
            variant="text"
            size="medium"
            style={styles.secondaryButton}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  scrollView: {
    flex: 1,
  },
  successHeader: {
    alignItems: 'center',
    padding: 40,
    paddingBottom: 20,
  },
  successIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#f0f9ff',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    borderWidth: 2,
    borderColor: '#bfdbfe',
  },
  successIconText: {
    fontSize: 40,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333333',
    textAlign: 'center',
    marginBottom: 8,
  },
  successMessage: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
  },
  detailsCard: {
    backgroundColor: '#f8fff8',
    borderColor: '#00A651',
    borderWidth: 1,
  },
  detailsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 20,
  },
  stepItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  stepNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#00A651',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    marginTop: 2,
  },
  stepNumberText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 4,
  },
  stepDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  actionsCard: {
    backgroundColor: '#f8f9fa',
  },
  actionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  actionsList: {
    gap: 16,
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  actionIcon: {
    fontSize: 24,
    marginRight: 12,
    marginTop: 2,
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  infoCard: {
    backgroundColor: '#fff9e6',
    borderColor: '#ffc107',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  feedbackCard: {
    backgroundColor: '#f0f8ff',
    borderRadius: 8,
    alignItems: 'center',
  },
  feedbackTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 4,
  },
  feedbackText: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 12,
    textAlign: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  ratingStar: {
    fontSize: 24,
    opacity: 0.3,
  },
  pocNotice: {
    backgroundColor: '#FFF3E0',
    borderRadius: 8,
  },
  pocContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pocIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  pocText: {
    fontSize: 14,
    color: '#FF6B35',
    flex: 1,
    lineHeight: 20,
  },
  footer: {
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  primaryButton: {
    marginBottom: 12,
  },
  secondaryButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  secondaryButton: {
    flex: 1,
    marginHorizontal: 4,
  },
});

export default ThankYou;
