package za.co.nedbank.ui.view.reactnative;

import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Button;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import za.co.nedbank.R;

/**
 * React Native Activity for Insurance Dashboard POC
 * This activity simulates the React Native insurance dashboard component
 * For the POC, we're using native Android views to demonstrate the concept
 */
public class ReactNativeInsuranceActivity extends AppCompatActivity {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Set up the toolbar/action bar
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("Insurance Dashboard (React Native POC)");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }

        createPOCLayout();
    }

    private void createPOCLayout() {
        // Create the main layout programmatically to simulate React Native UI
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setPadding(32, 32, 32, 32);
        mainLayout.setBackgroundColor(0xFFFFFFFF);

        // Header
        TextView headerTitle = new TextView(this);
        headerTitle.setText("My Insurance");
        headerTitle.setTextSize(22);
        headerTitle.setTextColor(0xFF333333);
        headerTitle.setPadding(0, 0, 0, 40);
        mainLayout.addView(headerTitle);

        // POC Notice
        TextView pocNotice = new TextView(this);
        pocNotice.setText("🚀 React Native POC - Insurance Dashboard");
        pocNotice.setTextSize(16);
        pocNotice.setTextColor(0xFFFF6B35);
        pocNotice.setBackgroundColor(0xFFFFF3E0);
        pocNotice.setPadding(32, 24, 32, 24);
        pocNotice.setCompoundDrawablePadding(16);
        mainLayout.addView(pocNotice);

        // Add some spacing
        View spacer1 = new View(this);
        spacer1.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, 40));
        mainLayout.addView(spacer1);

        // Manage Policies Button
        Button managePoliciesBtn = createActionButton("Manage policies and claims", "View and manage your existing policies");
        managePoliciesBtn.setOnClickListener(v -> showToast("Manage Policies clicked - React Native functionality"));
        mainLayout.addView(managePoliciesBtn);

        // Complete Applications Button
        Button completeAppsBtn = createActionButton("Complete applications", "Finish your pending applications");
        completeAppsBtn.setOnClickListener(v -> showToast("Complete Applications clicked - React Native functionality"));
        mainLayout.addView(completeAppsBtn);

        // Emergency Services Button
        Button emergencyBtn = createActionButton("Emergency services", "Access emergency assistance");
        emergencyBtn.setOnClickListener(v -> showToast("Emergency Services clicked - React Native functionality"));
        mainLayout.addView(emergencyBtn);

        // Add some spacing
        View spacer2 = new View(this);
        spacer2.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, 40));
        mainLayout.addView(spacer2);

        // Get Cover Section
        TextView getCoverTitle = new TextView(this);
        getCoverTitle.setText("Get Cover");
        getCoverTitle.setTextSize(22);
        getCoverTitle.setTextColor(0xFF333333);
        getCoverTitle.setPadding(0, 0, 0, 24);
        mainLayout.addView(getCoverTitle);

        // Product buttons in a horizontal layout
        LinearLayout productsLayout = new LinearLayout(this);
        productsLayout.setOrientation(LinearLayout.HORIZONTAL);

        Button funeralBtn = createProductButton("Funeral Cover");
        funeralBtn.setOnClickListener(v -> showToast("Funeral Cover selected - React Native functionality"));
        productsLayout.addView(funeralBtn);

        Button lifeBtn = createProductButton("Life Cover");
        lifeBtn.setOnClickListener(v -> showToast("Life Cover selected - React Native functionality"));
        productsLayout.addView(lifeBtn);

        mainLayout.addView(productsLayout);

        setContentView(mainLayout);
    }

    private Button createActionButton(String title, String subtitle) {
        Button button = new Button(this);
        button.setText(title + "\n" + subtitle);
        button.setTextSize(14);
        button.setTextColor(0xFF333333);
        button.setBackgroundColor(0xFFF5F5F5);
        button.setPadding(32, 24, 32, 24);

        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
        );
        params.setMargins(0, 0, 0, 16);
        button.setLayoutParams(params);

        return button;
    }

    private Button createProductButton(String title) {
        Button button = new Button(this);
        button.setText(title);
        button.setTextSize(12);
        button.setTextColor(0xFFFFFFFF);
        button.setBackgroundColor(0xFF00A651);
        button.setPadding(24, 16, 24, 16);

        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                0,
                LinearLayout.LayoutParams.WRAP_CONTENT,
                1.0f
        );
        params.setMargins(8, 0, 8, 0);
        button.setLayoutParams(params);

        return button;
    }

    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}
