package za.co.nedbank.ui.view.reactnative;

import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Button;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import za.co.nedbank.R;

/**
 * React Native Activity for Insurance Dashboard POC
 * This activity simulates the React Native insurance dashboard component
 * For the POC, we're using native Android views to demonstrate the concept
 */
public class ReactNativeInsuranceActivity extends AppCompatActivity {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Set up the toolbar/action bar
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("Insurance Dashboard (React Native POC)");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }

        createPOCLayout();
    }

    private void createPOCLayout() {
        // Create ScrollView to match the original layout
        androidx.core.widget.NestedScrollView scrollView = new androidx.core.widget.NestedScrollView(this);
        scrollView.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT));

        // Main container layout
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setBackgroundColor(0xFFFFFFFF);

        // My insurance section header - exact spacing from layout
        TextView myInsuranceTitle = new TextView(this);
        myInsuranceTitle.setText("My insurance");
        myInsuranceTitle.setTextSize(22); // dimen_22sp
        myInsuranceTitle.setTextColor(0xFF333333); // black_333333
        myInsuranceTitle.setTypeface(android.graphics.Typeface.create("sans-serif", android.graphics.Typeface.NORMAL));

        LinearLayout.LayoutParams titleParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        titleParams.setMargins(dpToPx(15), dpToPx(20), dpToPx(15), 0); // layout_marginStart/End="@dimen/dimen_15dp", paddingTop="@dimen/dimen_20dp"
        myInsuranceTitle.setLayoutParams(titleParams);
        mainLayout.addView(myInsuranceTitle);

        // Divider after title
        View divider1 = new View(this);
        divider1.setBackgroundColor(0xFFEEEEEE); // divider_background
        LinearLayout.LayoutParams dividerParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            dpToPx(1)
        );
        dividerParams.setMargins(0, dpToPx(20), 0, dpToPx(1));
        divider1.setLayoutParams(dividerParams);
        mainLayout.addView(divider1);

        // Policies and claims card
        LinearLayout policiesCard = createDashboardCard(
            "Policies and claims",
            "Manage cover and submit your claims."
        );
        policiesCard.setOnClickListener(v -> showToast("Policies and claims - React Native functionality"));
        mainLayout.addView(policiesCard);

        // Complete applications card
        LinearLayout applicationsCard = createDashboardCard(
            "Complete applications",
            "Complete or update your quote."
        );
        applicationsCard.setOnClickListener(v -> showToast("Complete applications - React Native functionality"));
        mainLayout.addView(applicationsCard);

        // Emergency services card
        LinearLayout emergencyCard = createDashboardCard(
            "Emergency services",
            "Information on whom to call when you have an emergency."
        );
        emergencyCard.setOnClickListener(v -> showToast("Emergency services - React Native functionality"));
        mainLayout.addView(emergencyCard);

        // Get cover section header
        TextView getCoverTitle = new TextView(this);
        getCoverTitle.setText("Get cover");
        getCoverTitle.setTextSize(22); // dimen_22sp
        getCoverTitle.setTextColor(0xFF333333); // black_333333
        getCoverTitle.setTypeface(android.graphics.Typeface.create("sans-serif", android.graphics.Typeface.NORMAL));

        LinearLayout.LayoutParams getCoverParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        getCoverParams.setMargins(dpToPx(15), dpToPx(20), dpToPx(15), 0);
        getCoverTitle.setLayoutParams(getCoverParams);
        mainLayout.addView(getCoverTitle);

        // For you card with notification badge
        LinearLayout forYouCard = createForYouCard();
        forYouCard.setOnClickListener(v -> showToast("For you offers - React Native functionality"));
        mainLayout.addView(forYouCard);

        // Product grid (2x2) using RecyclerView layout approach
        LinearLayout productContainer = createProductGrid();
        mainLayout.addView(productContainer);

        scrollView.addView(mainLayout);
        setContentView(scrollView);
    }

    // Helper method to convert dp to pixels
    private int dpToPx(int dp) {
        float density = getResources().getDisplayMetrics().density;
        return Math.round(dp * density);
    }

    private LinearLayout createDashboardCard(String title, String subtitle) {
        // Create a wrapper to return LinearLayout but use RelativeLayout internally
        LinearLayout wrapper = new LinearLayout(this);
        wrapper.setOrientation(LinearLayout.VERTICAL);
        wrapper.setLayoutParams(new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        ));

        // Create the exact layout structure from dashboard_manage_policy_layout.xml
        RelativeLayout cardLayout = new RelativeLayout(this);
        cardLayout.setId(View.generateViewId());
        cardLayout.setPadding(dpToPx(15), dpToPx(20), dpToPx(15), dpToPx(20));

        RelativeLayout.LayoutParams cardParams = new RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.MATCH_PARENT,
            RelativeLayout.LayoutParams.WRAP_CONTENT
        );
        cardLayout.setLayoutParams(cardParams);

        // Icon (40dp width) - will be set per card type
        TextView iconView = new TextView(this);
        iconView.setId(View.generateViewId());
        iconView.setText("📋"); // Default, will customize
        iconView.setTextSize(20);
        iconView.setWidth(dpToPx(40));

        RelativeLayout.LayoutParams iconParams = new RelativeLayout.LayoutParams(
            dpToPx(40),
            RelativeLayout.LayoutParams.WRAP_CONTENT
        );
        iconParams.addRule(RelativeLayout.ALIGN_PARENT_START);
        iconParams.topMargin = dpToPx(4);
        iconView.setLayoutParams(iconParams);
        cardLayout.addView(iconView);

        // Text container
        RelativeLayout textContainer = new RelativeLayout(this);
        textContainer.setId(View.generateViewId());

        RelativeLayout.LayoutParams textParams = new RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.WRAP_CONTENT,
            RelativeLayout.LayoutParams.WRAP_CONTENT
        );
        textParams.addRule(RelativeLayout.END_OF, iconView.getId());
        textParams.leftMargin = dpToPx(10);
        textContainer.setLayoutParams(textParams);

        // Title
        TextView titleView = new TextView(this);
        titleView.setId(View.generateViewId());
        titleView.setText(title);
        titleView.setTextSize(14); // dimen_14sp
        titleView.setTextColor(0xFF333333); // black_333333
        titleView.setTypeface(android.graphics.Typeface.create("sans-serif-medium", android.graphics.Typeface.NORMAL));

        RelativeLayout.LayoutParams titleParams = new RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.WRAP_CONTENT,
            RelativeLayout.LayoutParams.WRAP_CONTENT
        );
        titleView.setLayoutParams(titleParams);
        textContainer.addView(titleView);

        // Subtitle
        TextView subtitleView = new TextView(this);
        subtitleView.setText(subtitle);
        subtitleView.setTextSize(14); // dimen_14sp
        subtitleView.setTextColor(0xFF333333); // black_333333
        subtitleView.setTypeface(android.graphics.Typeface.create("sans-serif", android.graphics.Typeface.NORMAL));

        RelativeLayout.LayoutParams subtitleParams = new RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.WRAP_CONTENT,
            RelativeLayout.LayoutParams.WRAP_CONTENT
        );
        subtitleParams.addRule(RelativeLayout.BELOW, titleView.getId());
        subtitleParams.topMargin = dpToPx(5);
        subtitleView.setLayoutParams(subtitleParams);
        textContainer.addView(subtitleView);

        cardLayout.addView(textContainer);

        // Arrow icon
        TextView arrowView = new TextView(this);
        arrowView.setText(">");
        arrowView.setTextSize(18);
        arrowView.setTextColor(0xFF999999);

        RelativeLayout.LayoutParams arrowParams = new RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.WRAP_CONTENT,
            RelativeLayout.LayoutParams.WRAP_CONTENT
        );
        arrowParams.addRule(RelativeLayout.ALIGN_PARENT_END);
        arrowParams.addRule(RelativeLayout.CENTER_VERTICAL);
        arrowView.setLayoutParams(arrowParams);
        cardLayout.addView(arrowView);

        wrapper.addView(cardLayout);

        // Divider
        View divider = new View(this);
        divider.setBackgroundColor(0xFFEEEEEE); // divider_background

        LinearLayout.LayoutParams dividerParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            dpToPx(1)
        );
        dividerParams.bottomMargin = dpToPx(1);
        divider.setLayoutParams(dividerParams);
        wrapper.addView(divider);

        return wrapper;
    }

    private LinearLayout createForYouCard() {
        LinearLayout cardLayout = new LinearLayout(this);
        cardLayout.setOrientation(LinearLayout.HORIZONTAL);
        cardLayout.setPadding(32, 32, 32, 32);
        cardLayout.setBackgroundColor(0xFFFFFFFF);

        // Add border
        android.graphics.drawable.GradientDrawable border = new android.graphics.drawable.GradientDrawable();
        border.setColor(0xFFFFFFFF);
        border.setStroke(2, 0xFFE0E0E0);
        border.setCornerRadius(16);
        cardLayout.setBackground(border);

        // Icon
        TextView iconView = new TextView(this);
        iconView.setText("💰");
        iconView.setTextSize(24);
        iconView.setPadding(0, 0, 32, 0);
        cardLayout.addView(iconView);

        // Text container
        LinearLayout textContainer = new LinearLayout(this);
        textContainer.setOrientation(LinearLayout.VERTICAL);
        LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
        textContainer.setLayoutParams(textParams);

        // Title
        TextView titleView = new TextView(this);
        titleView.setText("For you");
        titleView.setTextSize(16);
        titleView.setTextColor(0xFF333333);
        titleView.setTypeface(null, android.graphics.Typeface.BOLD);
        titleView.setPadding(0, 0, 0, 8);
        textContainer.addView(titleView);

        // Subtitle
        TextView subtitleView = new TextView(this);
        subtitleView.setText("Your personalised offers.");
        subtitleView.setTextSize(14);
        subtitleView.setTextColor(0xFF666666);
        textContainer.addView(subtitleView);

        cardLayout.addView(textContainer);

        // Notification badge
        TextView badgeView = new TextView(this);
        badgeView.setText("1");
        badgeView.setTextSize(12);
        badgeView.setTextColor(0xFFFFFFFF);
        badgeView.setBackgroundColor(0xFF00A651);
        badgeView.setPadding(16, 8, 16, 8);
        badgeView.setGravity(android.view.Gravity.CENTER);

        // Make badge circular
        android.graphics.drawable.GradientDrawable badgeBg = new android.graphics.drawable.GradientDrawable();
        badgeBg.setColor(0xFF00A651);
        badgeBg.setCornerRadius(50);
        badgeView.setBackground(badgeBg);

        cardLayout.addView(badgeView);

        // Arrow
        TextView arrowView = new TextView(this);
        arrowView.setText(">");
        arrowView.setTextSize(18);
        arrowView.setTextColor(0xFF999999);
        arrowView.setPadding(16, 0, 0, 0);
        cardLayout.addView(arrowView);

        // Card layout params
        LinearLayout.LayoutParams cardParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
        );
        cardParams.setMargins(dpToPx(15), dpToPx(20), dpToPx(15), 0);
        cardLayout.setLayoutParams(cardParams);

        return cardLayout;
    }

    private LinearLayout createProductGrid() {
        LinearLayout gridContainer = new LinearLayout(this);
        gridContainer.setOrientation(LinearLayout.VERTICAL);

        // First row
        LinearLayout firstRow = new LinearLayout(this);
        firstRow.setOrientation(LinearLayout.HORIZONTAL);

        LinearLayout funeralCard = createProductCard("🕊️", "Funeral");
        funeralCard.setOnClickListener(v -> showToast("Funeral cover - React Native functionality"));
        firstRow.addView(funeralCard);

        View spacer1 = new View(this);
        spacer1.setLayoutParams(new LinearLayout.LayoutParams(32, LinearLayout.LayoutParams.MATCH_PARENT));
        firstRow.addView(spacer1);

        LinearLayout lifeCard = createProductCard("❤️", "Life");
        lifeCard.setOnClickListener(v -> showToast("Life cover - React Native functionality"));
        firstRow.addView(lifeCard);

        gridContainer.addView(firstRow);

        // Spacing between rows
        View rowSpacer = new View(this);
        rowSpacer.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, 32));
        gridContainer.addView(rowSpacer);

        // Second row
        LinearLayout secondRow = new LinearLayout(this);
        secondRow.setOrientation(LinearLayout.HORIZONTAL);

        LinearLayout vehicleCard = createProductCard("🏠", "Vehicle, home &\nvaluables");
        vehicleCard.setOnClickListener(v -> showToast("Vehicle, home & valuables - React Native functionality"));
        secondRow.addView(vehicleCard);

        View spacer2 = new View(this);
        spacer2.setLayoutParams(new LinearLayout.LayoutParams(32, LinearLayout.LayoutParams.MATCH_PARENT));
        secondRow.addView(spacer2);

        LinearLayout legalCard = createProductCard("📋", "Legal expenses");
        legalCard.setOnClickListener(v -> showToast("Legal expenses - React Native functionality"));
        secondRow.addView(legalCard);

        gridContainer.addView(secondRow);

        return gridContainer;
    }

    private LinearLayout createProductCard(String icon, String title) {
        LinearLayout cardLayout = new LinearLayout(this);
        cardLayout.setOrientation(LinearLayout.VERTICAL);
        cardLayout.setPadding(32, 48, 32, 48);
        cardLayout.setGravity(android.view.Gravity.CENTER);

        // Add border
        android.graphics.drawable.GradientDrawable border = new android.graphics.drawable.GradientDrawable();
        border.setColor(0xFFFFFFFF);
        border.setStroke(2, 0xFFE0E0E0);
        border.setCornerRadius(16);
        cardLayout.setBackground(border);

        // Icon
        TextView iconView = new TextView(this);
        iconView.setText(icon);
        iconView.setTextSize(32);
        iconView.setGravity(android.view.Gravity.CENTER);
        iconView.setPadding(0, 0, 0, 24);
        cardLayout.addView(iconView);

        // Title
        TextView titleView = new TextView(this);
        titleView.setText(title);
        titleView.setTextSize(14);
        titleView.setTextColor(0xFF333333);
        titleView.setGravity(android.view.Gravity.CENTER);
        titleView.setTypeface(null, android.graphics.Typeface.BOLD);
        cardLayout.addView(titleView);

        // Card layout params - equal width
        LinearLayout.LayoutParams cardParams = new LinearLayout.LayoutParams(
                0,
                200, // Fixed height
                1.0f
        );
        cardLayout.setLayoutParams(cardParams);

        return cardLayout;
    }

    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}
