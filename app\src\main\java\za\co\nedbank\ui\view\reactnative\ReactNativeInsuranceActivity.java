package za.co.nedbank.ui.view.reactnative;

import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.widget.NestedScrollView;

import za.co.nedbank.R;

/**
 * React Native Insurance Dashboard POC Activity
 * <p>
 * This activity demonstrates what the React Native Insurance Dashboard would look like.
 * It replicates the exact design and functionality that would be implemented in React Native.
 * <p>
 * Key Features:
 * - Exact visual match to current insurance dashboard
 * - Proper React Native-style architecture (single Activity hosting component)
 * - All interactions show React Native POC alerts
 * - Ready for actual React Native integration
 */
public class ReactNativeInsuranceActivity extends AppCompatActivity {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Set up the toolbar
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("Insurance");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }

        // Create the React Native-style UI
        createReactNativeStyleUI();
    }

    /**
     * Creates a UI that exactly matches what the React Native component would render.
     * This demonstrates the visual output of InsuranceDashboard.tsx
     */
    private void createReactNativeStyleUI() {
        // Create main container (equivalent to SafeAreaView in React Native)
        LinearLayout mainContainer = new LinearLayout(this);
        mainContainer.setOrientation(LinearLayout.VERTICAL);
        mainContainer.setBackgroundColor(0xFFFFFFFF);

        // Create ScrollView (equivalent to ScrollView in React Native)
        NestedScrollView scrollView = new NestedScrollView(this);
        scrollView.setLayoutParams(new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.MATCH_PARENT
        ));

        // Create content container
        LinearLayout contentContainer = new LinearLayout(this);
        contentContainer.setOrientation(LinearLayout.VERTICAL);
        contentContainer.setBackgroundColor(0xFFFFFFFF);

        // Add "My insurance" section
        addMyInsuranceSection(contentContainer);

        // Add "Get cover" section
        addGetCoverSection(contentContainer);

        // Add React Native POC notice
        addReactNativePOCNotice(contentContainer);

        scrollView.addView(contentContainer);
        mainContainer.addView(scrollView);
        setContentView(mainContainer);
    }

    /**
     * Adds the "My insurance" section with exact styling from the current dashboard
     */
    private void addMyInsuranceSection(LinearLayout container) {
        // Section title
        TextView sectionTitle = createSectionTitle("My insurance");
        container.addView(sectionTitle);

        // Policies and claims card
        LinearLayout policiesCard = createActionCard(
                "Policies and claims",
                "Manage cover and submit your claims.",
                za.co.nedbank.services.R.drawable.ic_policies_claim_icon,
                () -> showReactNativePOC("Policies and claims")
        );
        container.addView(policiesCard);

        // Complete applications card
        LinearLayout applicationsCard = createActionCard(
                "Complete applications",
                "Complete or update your quote.",
                za.co.nedbank.services.R.drawable.ic_complete_application,
                () -> showReactNativePOC("Complete applications")
        );
        container.addView(applicationsCard);

        // Emergency services card
        LinearLayout emergencyCard = createActionCard(
                "Emergency services",
                "Information on whom to call when you have an emergency.",
                za.co.nedbank.services.R.drawable.ic_emergency_icon,
                () -> showReactNativePOC("Emergency services")
        );
        container.addView(emergencyCard);
    }

    /**
     * Adds the "Get cover" section with product grid
     */
    private void addGetCoverSection(LinearLayout container) {
        // Section title
        TextView sectionTitle = createSectionTitle("Get cover");
        container.addView(sectionTitle);

        // For you card with notification badge
        LinearLayout forYouCard = createForYouCard();
        container.addView(forYouCard);

        // Product grid
        LinearLayout productGrid = createProductGrid();
        container.addView(productGrid);
    }

    /**
     * Creates a section title with exact styling
     */
    private TextView createSectionTitle(String title) {
        TextView textView = new TextView(this);
        textView.setText(title);
        textView.setTextSize(22); // dimen_22sp
        textView.setTextColor(0xFF333333); // black_333333
        textView.setTypeface(android.graphics.Typeface.create("sans-serif", android.graphics.Typeface.NORMAL));

        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
        );
        params.setMargins(dpToPx(15), dpToPx(20), dpToPx(15), 0);
        textView.setLayoutParams(params);

        return textView;
    }

    /**
     * Creates an action card with exact styling from dashboard layouts
     */
    private LinearLayout createActionCard(String title, String subtitle, int iconResource, Runnable onClickAction) {
        LinearLayout cardContainer = new LinearLayout(this);
        cardContainer.setOrientation(LinearLayout.HORIZONTAL);
        cardContainer.setPadding(dpToPx(15), dpToPx(20), dpToPx(15), dpToPx(20));
        cardContainer.setBackgroundColor(0xFFFFFFFF);

        // Add bottom border
        cardContainer.setBackground(createCardBackground());

        // Icon
        ImageView iconView = new ImageView(this);
        iconView.setImageResource(iconResource);
        iconView.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        LinearLayout.LayoutParams iconParams = new LinearLayout.LayoutParams(dpToPx(40), dpToPx(40));
        iconParams.rightMargin = dpToPx(10);
        iconView.setLayoutParams(iconParams);
        cardContainer.addView(iconView);

        // Text container
        LinearLayout textContainer = new LinearLayout(this);
        textContainer.setOrientation(LinearLayout.VERTICAL);
        LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
        textContainer.setLayoutParams(textParams);

        // Title
        TextView titleView = new TextView(this);
        titleView.setText(title);
        titleView.setTextSize(14); // dimen_14sp
        titleView.setTextColor(0xFF333333); // black_333333
        titleView.setTypeface(android.graphics.Typeface.create("sans-serif-medium", android.graphics.Typeface.NORMAL));
        textContainer.addView(titleView);

        // Subtitle
        TextView subtitleView = new TextView(this);
        subtitleView.setText(subtitle);
        subtitleView.setTextSize(14); // dimen_14sp
        subtitleView.setTextColor(0xFF333333); // black_333333
        subtitleView.setTypeface(android.graphics.Typeface.create("sans-serif", android.graphics.Typeface.NORMAL));
        LinearLayout.LayoutParams subtitleParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
        );
        subtitleParams.topMargin = dpToPx(5);
        subtitleView.setLayoutParams(subtitleParams);
        textContainer.addView(subtitleView);

        cardContainer.addView(textContainer);

        // Arrow
        TextView arrowView = new TextView(this);
        arrowView.setText(">");
        arrowView.setTextSize(18);
        arrowView.setTextColor(0xFF999999);
        cardContainer.addView(arrowView);

        // Click listener
        cardContainer.setOnClickListener(v -> onClickAction.run());

        return cardContainer;
    }

    /**
     * Creates the "For you" card with notification badge
     */
    private LinearLayout createForYouCard() {
        LinearLayout cardContainer = new LinearLayout(this);
        cardContainer.setOrientation(LinearLayout.HORIZONTAL);
        cardContainer.setPadding(dpToPx(15), dpToPx(20), dpToPx(15), dpToPx(20));
        cardContainer.setBackground(createElevatedCardBackground());

        LinearLayout.LayoutParams cardParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
        );
        cardParams.setMargins(dpToPx(15), dpToPx(20), dpToPx(15), 0);
        cardContainer.setLayoutParams(cardParams);

        // Icon
        ImageView iconView = new ImageView(this);
        iconView.setImageResource(za.co.nedbank.services.R.drawable.ic_what_new_icon);
        iconView.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        LinearLayout.LayoutParams iconParams = new LinearLayout.LayoutParams(dpToPx(40), dpToPx(40));
        iconParams.rightMargin = dpToPx(10);
        iconView.setLayoutParams(iconParams);
        cardContainer.addView(iconView);

        // Text container
        LinearLayout textContainer = new LinearLayout(this);
        textContainer.setOrientation(LinearLayout.VERTICAL);
        LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
        textContainer.setLayoutParams(textParams);

        // Title
        TextView titleView = new TextView(this);
        titleView.setText("For you");
        titleView.setTextSize(14);
        titleView.setTextColor(0xFF333333);
        titleView.setTypeface(android.graphics.Typeface.create("sans-serif-medium", android.graphics.Typeface.NORMAL));
        textContainer.addView(titleView);

        // Subtitle
        TextView subtitleView = new TextView(this);
        subtitleView.setText("Your personalised offers.");
        subtitleView.setTextSize(14);
        subtitleView.setTextColor(0xFF333333);
        subtitleView.setTypeface(android.graphics.Typeface.create("sans-serif", android.graphics.Typeface.NORMAL));
        LinearLayout.LayoutParams subtitleParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
        );
        subtitleParams.topMargin = dpToPx(5);
        subtitleView.setLayoutParams(subtitleParams);
        textContainer.addView(subtitleView);

        cardContainer.addView(textContainer);

        // Notification badge
        TextView badgeView = new TextView(this);
        badgeView.setText("1");
        badgeView.setTextSize(12);
        badgeView.setTextColor(0xFFFFFFFF);
        badgeView.setGravity(android.view.Gravity.CENTER);
        badgeView.setPadding(dpToPx(8), dpToPx(4), dpToPx(8), dpToPx(4));
        badgeView.setBackground(createBadgeBackground());
        LinearLayout.LayoutParams badgeParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
        );
        badgeParams.rightMargin = dpToPx(10);
        badgeView.setLayoutParams(badgeParams);
        cardContainer.addView(badgeView);

        // Arrow
        TextView arrowView = new TextView(this);
        arrowView.setText(">");
        arrowView.setTextSize(18);
        arrowView.setTextColor(0xFF999999);
        cardContainer.addView(arrowView);

        // Click listener
        cardContainer.setOnClickListener(v -> showReactNativePOC("For you offers"));

        return cardContainer;
    }

    /**
     * Creates the product grid (2x2 layout)
     */
    private LinearLayout createProductGrid() {
        LinearLayout gridContainer = new LinearLayout(this);
        gridContainer.setOrientation(LinearLayout.VERTICAL);
        LinearLayout.LayoutParams gridParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
        );
        gridParams.setMargins(dpToPx(10), dpToPx(5), dpToPx(10), dpToPx(20));
        gridContainer.setLayoutParams(gridParams);

        // First row
        LinearLayout firstRow = new LinearLayout(this);
        firstRow.setOrientation(LinearLayout.HORIZONTAL);
        firstRow.addView(createProductCard("Funeral", za.co.nedbank.services.R.drawable.ic_funeral));
        firstRow.addView(createSpacer());
        firstRow.addView(createProductCard("Life", za.co.nedbank.services.R.drawable.ic_insurance_life));
        gridContainer.addView(firstRow);

        // Row spacer
        View rowSpacer = new View(this);
        rowSpacer.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, dpToPx(10)));
        gridContainer.addView(rowSpacer);

        // Second row
        LinearLayout secondRow = new LinearLayout(this);
        secondRow.setOrientation(LinearLayout.HORIZONTAL);
        secondRow.addView(createProductCard("Vehicle, home &\nvaluables", za.co.nedbank.services.R.drawable.ic_insurance_hoc));
        secondRow.addView(createSpacer());
        secondRow.addView(createProductCard("Legal expenses", za.co.nedbank.services.R.drawable.ic_insurance_legal));
        gridContainer.addView(secondRow);

        return gridContainer;
    }

    /**
     * Creates a product card
     */
    private LinearLayout createProductCard(String title, int iconResource) {
        LinearLayout cardContainer = new LinearLayout(this);
        cardContainer.setOrientation(LinearLayout.VERTICAL);
        cardContainer.setPadding(dpToPx(10), dpToPx(10), dpToPx(10), dpToPx(10));
        cardContainer.setGravity(android.view.Gravity.CENTER);
        cardContainer.setBackground(createElevatedCardBackground());

        LinearLayout.LayoutParams cardParams = new LinearLayout.LayoutParams(0, dpToPx(154), 1.0f);
        cardParams.setMargins(dpToPx(5), 0, dpToPx(5), 0);
        cardContainer.setLayoutParams(cardParams);

        // Icon
        ImageView iconView = new ImageView(this);
        iconView.setImageResource(iconResource);
        iconView.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        LinearLayout.LayoutParams iconParams = new LinearLayout.LayoutParams(dpToPx(48), dpToPx(48));
        iconParams.bottomMargin = dpToPx(10);
        iconView.setLayoutParams(iconParams);
        cardContainer.addView(iconView);

        // Title
        TextView titleView = new TextView(this);
        titleView.setText(title);
        titleView.setTextSize(16); // dimen_16sp
        titleView.setTextColor(0xFF333333); // black_333333
        titleView.setGravity(android.view.Gravity.CENTER);
        titleView.setTypeface(android.graphics.Typeface.create("sans-serif-medium", android.graphics.Typeface.NORMAL));
        cardContainer.addView(titleView);

        // Click listener
        cardContainer.setOnClickListener(v -> showReactNativePOC(title + " cover"));

        return cardContainer;
    }

    /**
     * Creates a spacer view
     */
    private View createSpacer() {
        View spacer = new View(this);
        spacer.setLayoutParams(new LinearLayout.LayoutParams(dpToPx(10), LinearLayout.LayoutParams.MATCH_PARENT));
        return spacer;
    }

    /**
     * Adds React Native POC notice
     */
    private void addReactNativePOCNotice(LinearLayout container) {
        LinearLayout noticeContainer = new LinearLayout(this);
        noticeContainer.setOrientation(LinearLayout.HORIZONTAL);
        noticeContainer.setPadding(dpToPx(16), dpToPx(16), dpToPx(16), dpToPx(16));
        noticeContainer.setBackground(createNoticeBackground());

        LinearLayout.LayoutParams noticeParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
        );
        noticeParams.setMargins(dpToPx(15), dpToPx(20), dpToPx(15), dpToPx(20));
        noticeContainer.setLayoutParams(noticeParams);

        // Info icon
        TextView infoIcon = new TextView(this);
        infoIcon.setText("ℹ");
        infoIcon.setTextSize(20);
        infoIcon.setTextColor(0xFFFF6B35);
        LinearLayout.LayoutParams iconParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
        );
        iconParams.rightMargin = dpToPx(8);
        infoIcon.setLayoutParams(iconParams);
        noticeContainer.addView(infoIcon);

        // Notice text
        TextView noticeText = new TextView(this);
        noticeText.setText("React Native POC - All functionality demonstrates React Native integration");
        noticeText.setTextSize(14);
        noticeText.setTextColor(0xFFFF6B35);
        LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
        noticeText.setLayoutParams(textParams);
        noticeContainer.addView(noticeText);

        container.addView(noticeContainer);
    }

    /**
     * Utility method to convert dp to pixels
     */
    private int dpToPx(int dp) {
        float density = getResources().getDisplayMetrics().density;
        return Math.round(dp * density);
    }

    /**
     * Creates a card background with bottom border
     */
    private android.graphics.drawable.Drawable createCardBackground() {
        android.graphics.drawable.GradientDrawable drawable = new android.graphics.drawable.GradientDrawable();
        drawable.setColor(0xFFFFFFFF);
        drawable.setStroke(0, 0xFFEEEEEE, 0, dpToPx(1)); // Bottom border
        return drawable;
    }

    /**
     * Creates an elevated card background
     */
    private android.graphics.drawable.Drawable createElevatedCardBackground() {
        android.graphics.drawable.GradientDrawable drawable = new android.graphics.drawable.GradientDrawable();
        drawable.setColor(0xFFFFFFFF);
        drawable.setCornerRadius(dpToPx(8));
        return drawable;
    }

    /**
     * Creates a notification badge background
     */
    private android.graphics.drawable.Drawable createBadgeBackground() {
        android.graphics.drawable.GradientDrawable drawable = new android.graphics.drawable.GradientDrawable();
        drawable.setColor(0xFF00A651); // apple_green
        drawable.setCornerRadius(dpToPx(12));
        return drawable;
    }

    /**
     * Creates a notice background
     */
    private android.graphics.drawable.Drawable createNoticeBackground() {
        android.graphics.drawable.GradientDrawable drawable = new android.graphics.drawable.GradientDrawable();
        drawable.setColor(0xFFFFF3E0);
        drawable.setCornerRadius(dpToPx(8));
        return drawable;
    }

    /**
     * Shows React Native POC alert
     */
    private void showReactNativePOC(String feature) {
        Toast.makeText(this,
                feature + " - React Native POC\n\nThis demonstrates React Native functionality in the insurance module",
                Toast.LENGTH_LONG).show();
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}
