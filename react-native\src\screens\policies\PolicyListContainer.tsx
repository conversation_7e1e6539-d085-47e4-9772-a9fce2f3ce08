import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';

import Button from '../../components/common/Button';
import Card from '../../components/common/Card';

interface Policy {
  id: string;
  type: string;
  policyNumber: string;
  status: 'Active' | 'Pending' | 'Expired' | 'Cancelled';
  premium: number;
  coverAmount: string;
  nextPayment: string;
  description: string;
}

interface Application {
  id: string;
  type: string;
  referenceNumber: string;
  status: 'Under Review' | 'Approved' | 'Declined' | 'Pending Documents';
  submittedDate: string;
  description: string;
}

const PolicyListContainer: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'policies' | 'applications'>('policies');
  const [refreshing, setRefreshing] = useState(false);
  const [policies, setPolicies] = useState<Policy[]>([]);
  const [applications, setApplications] = useState<Application[]>([]);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    // Simulate loading policies and applications
    setPolicies([
      {
        id: '1',
        type: 'Funeral Cover',
        policyNumber: 'FUN001234567',
        status: 'Active',
        premium: 75.00,
        coverAmount: 'R30,000',
        nextPayment: '2024-02-15',
        description: 'Funeral 30K Plan',
      },
      {
        id: '2',
        type: 'Life Cover',
        policyNumber: 'LIFE987654321',
        status: 'Active',
        premium: 150.00,
        coverAmount: 'R500,000',
        nextPayment: '2024-02-20',
        description: 'MyCover Life Insurance',
      },
    ]);

    setApplications([
      {
        id: '1',
        type: 'Vehicle Insurance',
        referenceNumber: 'VEH202401001',
        status: 'Under Review',
        submittedDate: '2024-01-15',
        description: 'MyCover Motor Insurance',
      },
      {
        id: '2',
        type: 'Home Contents',
        referenceNumber: 'HOC202401002',
        status: 'Pending Documents',
        submittedDate: '2024-01-10',
        description: 'Household Contents Cover',
      },
    ]);
  };

  const onRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      loadData();
      setRefreshing(false);
    }, 1000);
  };

  const handlePolicyPress = (policy: Policy) => {
    Alert.alert(
      'React Native POC',
      `Policy: ${policy.type}\nNumber: ${policy.policyNumber}\nStatus: ${policy.status}\n\nThis would navigate to detailed policy management screen.`,
      [{ text: 'OK' }]
    );
  };

  const handleApplicationPress = (application: Application) => {
    Alert.alert(
      'React Native POC',
      `Application: ${application.type}\nReference: ${application.referenceNumber}\nStatus: ${application.status}\n\nThis would show application details and tracking.`,
      [{ text: 'OK' }]
    );
  };

  const handleSubmitClaim = () => {
    Alert.alert(
      'React Native POC',
      'This would navigate to the claims submission flow, demonstrating React Native\'s capability for complex insurance processes.',
      [{ text: 'OK' }]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
      case 'Approved':
        return '#00A651';
      case 'Pending':
      case 'Under Review':
      case 'Pending Documents':
        return '#FF6B35';
      case 'Expired':
      case 'Cancelled':
      case 'Declined':
        return '#dc3545';
      default:
        return '#666666';
    }
  };

  const renderPolicyCard = (policy: Policy) => (
    <Card
      key={policy.id}
      style={styles.policyCard}
      onPress={() => handlePolicyPress(policy)}
      variant="default"
      padding="medium"
      margin="small"
    >
      <View style={styles.policyHeader}>
        <Text style={styles.policyType}>{policy.type}</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(policy.status) }]}>
          <Text style={styles.statusText}>{policy.status}</Text>
        </View>
      </View>
      
      <Text style={styles.policyNumber}>Policy: {policy.policyNumber}</Text>
      <Text style={styles.policyDescription}>{policy.description}</Text>
      
      <View style={styles.policyDetails}>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Cover amount</Text>
          <Text style={styles.detailValue}>{policy.coverAmount}</Text>
        </View>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Monthly premium</Text>
          <Text style={styles.premiumValue}>R{policy.premium.toFixed(2)}</Text>
        </View>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Next payment</Text>
          <Text style={styles.detailValue}>{policy.nextPayment}</Text>
        </View>
      </View>
    </Card>
  );

  const renderApplicationCard = (application: Application) => (
    <Card
      key={application.id}
      style={styles.applicationCard}
      onPress={() => handleApplicationPress(application)}
      variant="default"
      padding="medium"
      margin="small"
    >
      <View style={styles.applicationHeader}>
        <Text style={styles.applicationType}>{application.type}</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(application.status) }]}>
          <Text style={styles.statusText}>{application.status}</Text>
        </View>
      </View>
      
      <Text style={styles.applicationReference}>Ref: {application.referenceNumber}</Text>
      <Text style={styles.applicationDescription}>{application.description}</Text>
      
      <View style={styles.applicationDetails}>
        <Text style={styles.submittedDate}>Submitted: {application.submittedDate}</Text>
      </View>
    </Card>
  );

  return (
    <View style={styles.container}>
      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'policies' && styles.activeTab]}
          onPress={() => setActiveTab('policies')}
        >
          <Text style={[styles.tabText, activeTab === 'policies' && styles.activeTabText]}>
            My Policies
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'applications' && styles.activeTab]}
          onPress={() => setActiveTab('applications')}
        >
          <Text style={[styles.tabText, activeTab === 'applications' && styles.activeTabText]}>
            Applications
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {activeTab === 'policies' ? (
          <View style={styles.content}>
            <View style={styles.header}>
              <Text style={styles.title}>My Policies</Text>
              <Text style={styles.subtitle}>
                Manage your active insurance policies and submit claims.
              </Text>
            </View>

            {policies.length > 0 ? (
              <View style={styles.policiesContainer}>
                {policies.map(renderPolicyCard)}
              </View>
            ) : (
              <Card style={styles.emptyCard} variant="outlined" padding="large" margin="medium">
                <Text style={styles.emptyTitle}>No policies found</Text>
                <Text style={styles.emptyText}>
                  You don't have any active policies yet. Get a quote to start your coverage.
                </Text>
              </Card>
            )}

            <View style={styles.actionSection}>
              <Button
                title="Submit a Claim"
                onPress={handleSubmitClaim}
                variant="primary"
                fullWidth
                size="large"
              />
            </View>
          </View>
        ) : (
          <View style={styles.content}>
            <View style={styles.header}>
              <Text style={styles.title}>My Applications</Text>
              <Text style={styles.subtitle}>
                Track the status of your insurance applications.
              </Text>
            </View>

            {applications.length > 0 ? (
              <View style={styles.applicationsContainer}>
                {applications.map(renderApplicationCard)}
              </View>
            ) : (
              <Card style={styles.emptyCard} variant="outlined" padding="large" margin="medium">
                <Text style={styles.emptyTitle}>No applications found</Text>
                <Text style={styles.emptyText}>
                  You don't have any pending applications.
                </Text>
              </Card>
            )}
          </View>
        )}

        {/* React Native POC Notice */}
        <Card style={styles.pocNotice} variant="flat" padding="medium" margin="medium">
          <View style={styles.pocContent}>
            <Text style={styles.pocIcon}>📋</Text>
            <Text style={styles.pocText}>
              React Native POC - Policy management with real-time data and interactive UI components
            </Text>
          </View>
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#f8f8f8',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#00A651',
    backgroundColor: '#ffffff',
  },
  tabText: {
    fontSize: 16,
    color: '#666666',
  },
  activeTabText: {
    color: '#00A651',
    fontWeight: '500',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    lineHeight: 22,
  },
  policiesContainer: {
    paddingHorizontal: 10,
  },
  applicationsContainer: {
    paddingHorizontal: 10,
  },
  policyCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#00A651',
  },
  applicationCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#FF6B35',
  },
  policyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  applicationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  policyType: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
  },
  applicationType: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: '#ffffff',
    fontWeight: '500',
  },
  policyNumber: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  applicationReference: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  policyDescription: {
    fontSize: 14,
    color: '#333333',
    marginBottom: 12,
  },
  applicationDescription: {
    fontSize: 14,
    color: '#333333',
    marginBottom: 12,
  },
  policyDetails: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 12,
  },
  applicationDetails: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 12,
  },
  detailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666666',
  },
  detailValue: {
    fontSize: 14,
    color: '#333333',
    fontWeight: '500',
  },
  premiumValue: {
    fontSize: 14,
    color: '#00A651',
    fontWeight: '600',
  },
  submittedDate: {
    fontSize: 14,
    color: '#666666',
  },
  emptyCard: {
    alignItems: 'center',
    marginHorizontal: 20,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
  },
  actionSection: {
    padding: 20,
  },
  pocNotice: {
    backgroundColor: '#FFF3E0',
    borderRadius: 8,
  },
  pocContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pocIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  pocText: {
    fontSize: 14,
    color: '#FF6B35',
    flex: 1,
    lineHeight: 20,
  },
});

export default PolicyListContainer;
