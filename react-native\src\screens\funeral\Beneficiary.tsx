import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TextInput,
  Alert,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { FuneralStackParamList } from '../../navigation/FuneralNavigator';

import Button from '../../components/common/Button';
import Card from '../../components/common/Card';

type NavigationProp = StackNavigationProp<FuneralStackParamList, 'Beneficiary'>;
type RouteProp = RouteProp<FuneralStackParamList, 'Beneficiary'>;

interface BeneficiaryForm {
  firstName: string;
  lastName: string;
  idNumber: string;
  relationship: string;
  contactNumber: string;
  email: string;
}

const FuneralBeneficiary: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RouteProp>();
  const { planDetails } = route.params;

  const [form, setForm] = useState<BeneficiaryForm>({
    firstName: '',
    lastName: '',
    idNumber: '',
    relationship: '',
    contactNumber: '',
    email: '',
  });
  const [loading, setLoading] = useState(false);

  const updateForm = (field: keyof BeneficiaryForm, value: string) => {
    setForm(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = (): boolean => {
    if (!form.firstName.trim()) {
      Alert.alert('Validation Error', 'Please enter the beneficiary\'s first name.');
      return false;
    }
    if (!form.lastName.trim()) {
      Alert.alert('Validation Error', 'Please enter the beneficiary\'s last name.');
      return false;
    }
    if (!form.idNumber.trim() || form.idNumber.length !== 13) {
      Alert.alert('Validation Error', 'Please enter a valid 13-digit ID number.');
      return false;
    }
    if (!form.relationship.trim()) {
      Alert.alert('Validation Error', 'Please specify the relationship to the beneficiary.');
      return false;
    }
    if (!form.contactNumber.trim() || form.contactNumber.length < 10) {
      Alert.alert('Validation Error', 'Please enter a valid contact number.');
      return false;
    }
    if (!form.email.trim() || !form.email.includes('@')) {
      Alert.alert('Validation Error', 'Please enter a valid email address.');
      return false;
    }
    return true;
  };

  const handleContinue = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      // Show React Native POC indicator
      Alert.alert(
        'React Native POC',
        `Beneficiary: ${form.firstName} ${form.lastName}\nPlan: ${planDetails.title}\n\nThis demonstrates React Native form handling and validation in the insurance module.`,
        [
          {
            text: 'Continue',
            onPress: () => {
              navigation.navigate('DebitOrder', {
                planDetails,
                beneficiaryDetails: form,
              });
            },
          },
        ]
      );
      setLoading(false);
    }, 1000);
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Plan Summary */}
        <Card style={styles.planSummary} variant="outlined" padding="medium" margin="medium">
          <Text style={styles.planSummaryTitle}>Selected Plan</Text>
          <Text style={styles.planSummaryText}>{planDetails.title}</Text>
          <Text style={styles.planSummarySubtext}>{planDetails.coverAmount}</Text>
        </Card>

        <View style={styles.header}>
          <Text style={styles.title}>Beneficiary details</Text>
          <Text style={styles.subtitle}>
            Please provide the details of the person who will receive the benefit.
          </Text>
        </View>

        <Card style={styles.formCard} variant="default" padding="large" margin="medium">
          {/* First Name */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>First name *</Text>
            <TextInput
              style={styles.input}
              value={form.firstName}
              onChangeText={(value) => updateForm('firstName', value)}
              placeholder="Enter first name"
              placeholderTextColor="#999999"
            />
          </View>

          {/* Last Name */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Last name *</Text>
            <TextInput
              style={styles.input}
              value={form.lastName}
              onChangeText={(value) => updateForm('lastName', value)}
              placeholder="Enter last name"
              placeholderTextColor="#999999"
            />
          </View>

          {/* ID Number */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>ID number *</Text>
            <TextInput
              style={styles.input}
              value={form.idNumber}
              onChangeText={(value) => updateForm('idNumber', value)}
              placeholder="Enter 13-digit ID number"
              placeholderTextColor="#999999"
              keyboardType="numeric"
              maxLength={13}
            />
          </View>

          {/* Relationship */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Relationship *</Text>
            <TextInput
              style={styles.input}
              value={form.relationship}
              onChangeText={(value) => updateForm('relationship', value)}
              placeholder="e.g., Spouse, Child, Parent"
              placeholderTextColor="#999999"
            />
          </View>

          {/* Contact Number */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Contact number *</Text>
            <TextInput
              style={styles.input}
              value={form.contactNumber}
              onChangeText={(value) => updateForm('contactNumber', value)}
              placeholder="Enter contact number"
              placeholderTextColor="#999999"
              keyboardType="phone-pad"
            />
          </View>

          {/* Email */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Email address *</Text>
            <TextInput
              style={styles.input}
              value={form.email}
              onChangeText={(value) => updateForm('email', value)}
              placeholder="Enter email address"
              placeholderTextColor="#999999"
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>
        </Card>

        {/* React Native POC Notice */}
        <Card style={styles.pocNotice} variant="flat" padding="medium" margin="medium">
          <View style={styles.pocContent}>
            <Text style={styles.pocIcon}>⚛️</Text>
            <Text style={styles.pocText}>
              React Native POC - Form validation and state management working seamlessly across platforms
            </Text>
          </View>
        </Card>
      </ScrollView>

      <View style={styles.footer}>
        <Button
          title="Continue"
          onPress={handleContinue}
          loading={loading}
          fullWidth
          size="large"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  scrollView: {
    flex: 1,
  },
  planSummary: {
    backgroundColor: '#f8fff8',
    borderColor: '#00A651',
  },
  planSummaryTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
    marginBottom: 4,
  },
  planSummaryText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 2,
  },
  planSummarySubtext: {
    fontSize: 14,
    color: '#00A651',
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    lineHeight: 22,
  },
  formCard: {
    marginHorizontal: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333333',
    backgroundColor: '#ffffff',
  },
  pocNotice: {
    backgroundColor: '#FFF3E0',
    borderRadius: 8,
  },
  pocContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pocIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  pocText: {
    fontSize: 14,
    color: '#FF6B35',
    flex: 1,
    lineHeight: 20,
  },
  footer: {
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
});

export default FuneralBeneficiary;
