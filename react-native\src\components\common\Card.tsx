import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  GestureResponderEvent,
} from 'react-native';

interface CardProps {
  children: React.ReactNode;
  onPress?: (event: GestureResponderEvent) => void;
  style?: ViewStyle;
  variant?: 'default' | 'elevated' | 'outlined' | 'flat';
  padding?: 'none' | 'small' | 'medium' | 'large';
  margin?: 'none' | 'small' | 'medium' | 'large';
  disabled?: boolean;
}

const Card: React.FC<CardProps> = ({
  children,
  onPress,
  style,
  variant = 'default',
  padding = 'medium',
  margin = 'none',
  disabled = false,
}) => {
  const getCardStyle = (): ViewStyle => {
    const baseStyle = [
      styles.card,
      styles[`${variant}Card`],
      styles[`${padding}Padding`],
      styles[`${margin}Margin`],
    ];
    
    if (disabled) {
      baseStyle.push(styles.disabled);
    }
    
    if (style) {
      baseStyle.push(style);
    }
    
    return StyleSheet.flatten(baseStyle);
  };

  if (onPress && !disabled) {
    return (
      <TouchableOpacity
        style={getCardStyle()}
        onPress={onPress}
        activeOpacity={0.7}
        disabled={disabled}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={getCardStyle()}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 8,
    backgroundColor: '#ffffff',
  },
  
  // Variants
  defaultCard: {
    // Basic card with subtle shadow
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  elevatedCard: {
    // More prominent shadow
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
  },
  outlinedCard: {
    // Border instead of shadow
    borderWidth: 1,
    borderColor: '#e0e0e0',
    elevation: 0,
    shadowOpacity: 0,
  },
  flatCard: {
    // No shadow or border
    elevation: 0,
    shadowOpacity: 0,
  },
  
  // Padding variants
  nonePadding: {
    padding: 0,
  },
  smallPadding: {
    padding: 8,
  },
  mediumPadding: {
    padding: 16,
  },
  largePadding: {
    padding: 24,
  },
  
  // Margin variants
  noneMargin: {
    margin: 0,
  },
  smallMargin: {
    margin: 8,
  },
  mediumMargin: {
    margin: 16,
  },
  largeMargin: {
    margin: 24,
  },
  
  // Disabled state
  disabled: {
    opacity: 0.5,
  },
});

export default Card;
