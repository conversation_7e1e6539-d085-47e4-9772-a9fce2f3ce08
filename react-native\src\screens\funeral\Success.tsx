import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import { useRoute } from '@react-navigation/native';
import { RouteProp } from '@react-navigation/native';
import { FuneralStackParamList } from '../../navigation/FuneralNavigator';

import Button from '../../components/common/Button';
import Card from '../../components/common/Card';

type RouteProp = RouteProp<FuneralStackParamList, 'Success'>;

const FuneralSuccess: React.FC = () => {
  const route = useRoute<RouteProp>();
  const { quoteReference, premium } = route.params;

  const handleViewPolicies = () => {
    Alert.alert(
      'React Native POC',
      'This would navigate to the Policy List screen, demonstrating seamless navigation between React Native screens.',
      [{ text: 'OK' }]
    );
  };

  const handleDownloadQuote = () => {
    Alert.alert(
      'React Native POC',
      'This would download the quote PDF, demonstrating React Native file handling capabilities.',
      [{ text: 'OK' }]
    );
  };

  const handleBackToDashboard = () => {
    Alert.alert(
      'React Native POC',
      'This would navigate back to the main insurance dashboard, completing the user journey.',
      [{ text: 'OK' }]
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Success Icon and Message */}
        <View style={styles.successHeader}>
          <View style={styles.successIcon}>
            <Text style={styles.successIconText}>✓</Text>
          </View>
          <Text style={styles.successTitle}>Application submitted successfully!</Text>
          <Text style={styles.successSubtitle}>
            Your funeral cover application has been received and is being processed.
          </Text>
        </View>

        {/* Application Details */}
        <Card style={styles.detailsCard} variant="elevated" padding="large" margin="medium">
          <Text style={styles.cardTitle}>Application Details</Text>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Reference number:</Text>
            <Text style={styles.detailValue}>{quoteReference}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Monthly premium:</Text>
            <Text style={styles.premiumValue}>R{premium.toFixed(2)}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Application date:</Text>
            <Text style={styles.detailValue}>{new Date().toLocaleDateString()}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Status:</Text>
            <Text style={styles.statusValue}>Under review</Text>
          </View>
        </Card>

        {/* Next Steps */}
        <Card style={styles.stepsCard} variant="default" padding="large" margin="medium">
          <Text style={styles.cardTitle}>What happens next?</Text>
          
          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>1</Text>
            </View>
            <View style={styles.stepContent}>
              <Text style={styles.stepTitle}>Application review</Text>
              <Text style={styles.stepDescription}>
                We'll review your application within 2-3 business days.
              </Text>
            </View>
          </View>
          
          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>2</Text>
            </View>
            <View style={styles.stepContent}>
              <Text style={styles.stepTitle}>Confirmation</Text>
              <Text style={styles.stepDescription}>
                You'll receive an email confirmation once approved.
              </Text>
            </View>
          </View>
          
          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>3</Text>
            </View>
            <View style={styles.stepContent}>
              <Text style={styles.stepTitle}>Policy documents</Text>
              <Text style={styles.stepDescription}>
                Your policy documents will be sent via email and post.
              </Text>
            </View>
          </View>
        </Card>

        {/* Important Information */}
        <Card style={styles.infoCard} variant="outlined" padding="medium" margin="medium">
          <Text style={styles.infoTitle}>Important Information</Text>
          <Text style={styles.infoText}>
            • Keep your reference number safe for future reference{'\n'}
            • Your first premium will be debited next month{'\n'}
            • You can track your application status online{'\n'}
            • Contact us if you have any questions
          </Text>
        </Card>

        {/* React Native POC Notice */}
        <Card style={styles.pocNotice} variant="flat" padding="medium" margin="medium">
          <View style={styles.pocContent}>
            <Text style={styles.pocIcon}>🎉</Text>
            <Text style={styles.pocText}>
              React Native POC Complete! This demonstrates a full insurance application flow with professional UI and seamless navigation.
            </Text>
          </View>
        </Card>
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.footer}>
        <Button
          title="View My Policies"
          onPress={handleViewPolicies}
          variant="primary"
          fullWidth
          size="large"
          style={styles.primaryButton}
        />
        
        <View style={styles.secondaryButtons}>
          <Button
            title="Download Quote"
            onPress={handleDownloadQuote}
            variant="outline"
            size="medium"
            style={styles.secondaryButton}
          />
          
          <Button
            title="Back to Dashboard"
            onPress={handleBackToDashboard}
            variant="text"
            size="medium"
            style={styles.secondaryButton}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  scrollView: {
    flex: 1,
  },
  successHeader: {
    alignItems: 'center',
    padding: 40,
    paddingBottom: 20,
  },
  successIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#00A651',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  successIconText: {
    fontSize: 40,
    color: '#ffffff',
    fontWeight: 'bold',
  },
  successTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333333',
    textAlign: 'center',
    marginBottom: 8,
  },
  successSubtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
  },
  detailsCard: {
    backgroundColor: '#f8fff8',
    borderColor: '#00A651',
    borderWidth: 1,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666666',
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
  },
  premiumValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#00A651',
  },
  statusValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#FF6B35',
  },
  stepsCard: {
    marginHorizontal: 20,
  },
  stepItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  stepNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#00A651',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    marginTop: 2,
  },
  stepNumberText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 4,
  },
  stepDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  infoCard: {
    backgroundColor: '#fff9e6',
    borderColor: '#ffc107',
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  pocNotice: {
    backgroundColor: '#FFF3E0',
    borderRadius: 8,
  },
  pocContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pocIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  pocText: {
    fontSize: 14,
    color: '#FF6B35',
    flex: 1,
    lineHeight: 20,
  },
  footer: {
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  primaryButton: {
    marginBottom: 12,
  },
  secondaryButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  secondaryButton: {
    flex: 1,
    marginHorizontal: 4,
  },
});

export default FuneralSuccess;
