package za.co.nedbank.services.insurance.view.dashboard.policy_info.manage_policy

import org.junit.Before
import org.junit.Test
import org.mockito.ArgumentMatchers.any
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import za.co.nedbank.core.feature.FeatureConstants
import za.co.nedbank.core.feature.FeatureSetController
import za.co.nedbank.core.navigation.NavigationRouter
import za.co.nedbank.core.navigation.NavigationTarget
import za.co.nedbank.core.tracking.Analytics
import za.co.nedbank.core.tracking.InsuranceTrackingEvent
import za.co.nedbank.core.tracking.TrackingEvent
import za.co.nedbank.core.tracking.adobe.AdobeContextData
import za.co.nedbank.core.utils.StringUtils
import za.co.nedbank.services.insurance.domain.usecase.vvap.claim.VVAPPolicyDetailUseCase
import za.co.nedbank.services.insurance.view.dashboard.other.helper.VVAPPolicyParams
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants.AnalyticsConstants.SUBPRODUCT_CSF
import za.co.nedbank.services.insurance.view.other.mapper.vvaps.claim.PolicyDetailRequestViewToDataMapper
import za.co.nedbank.services.insurance.view.other.model.response.dashboard.retrieve_policy.AccountViewModel
import za.co.nedbank.services.insurance.view.vvap.policy_admin.other.helper.PolicyFlowFlags
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget
import kotlin.String

class ManagePolicyViewModelViewModelFragmentPresenterTest {
    @Mock
    private val mView: ManagePolicyView? = null

    @Mock
    private val mNavigationRouter: NavigationRouter? = null

    @Mock
    private val mFeatureSetController: FeatureSetController? = null

    @Mock
    private val mAnalytics: Analytics? = null

    @Mock
    private val policyDetailUseCase: VVAPPolicyDetailUseCase? = null

    @Mock
    private val requestViewToDataMapper: PolicyDetailRequestViewToDataMapper? = null

    @InjectMocks
    private val mFragmentPresenter: ManagePolicyPresenter? = null

    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)

        // Mock the mapper to return a non-null value BEFORE binding
        val mockRequestDataModel = Mockito.mock(za.co.nedbank.services.insurance.domain.data.request.vvaps.claim.policy_detail.PolicyDetailRequestDataModel::class.java)
        Mockito.`when`(requestViewToDataMapper!!.map(any())).thenReturn(mockRequestDataModel)

        mFragmentPresenter!!.bind(mView)
    }

    @Test
    fun checkActiveNIFPPolicy_Success() {
        val isActivePolicy = true
        val isNIFPPolicy = true
        val isHocClaim = false
        val isPolicyMcl = false
        val isPolicyPl = false
        val isPolicyLegalExpense = false
        mFragmentPresenter!!.checkPolicyAndShowView(
            isActivePolicy, isNIFPPolicy, isHocClaim, isPolicyMcl, isPolicyPl, isPolicyLegalExpense
        )
        Mockito.verify(mView)?.showFuneralManagePolicyView()
    }

    @Test
    fun checkActiveHocPolicy_Success() {
        val isActivePolicy = true
        val isNIFPPolicy = false
        val isHocClaim = true
        val isPolicyMcl = false
        val isPolicyPl = false
        val isPolicyLegalExpense = false
        mFragmentPresenter!!.checkPolicyAndShowView(
            isActivePolicy, isNIFPPolicy, isHocClaim, isPolicyMcl, isPolicyPl, isPolicyLegalExpense
        )
        Mockito.verify(mView)?.showHocClaimView()
    }

    @Test
    fun checkActiveNonNIFPPolicy_Success() {
        val isActivePolicy = true
        val isNIFPPolicy = false
        val isHocClaim = false
        val isPolicyMcl = false
        val isPolicyPl = false
        val isPolicyLegalExpense = false
        mFragmentPresenter!!.checkPolicyAndShowView(
            isActivePolicy, isNIFPPolicy, isHocClaim, isPolicyMcl, isPolicyPl, isPolicyLegalExpense
        )
        Mockito.verify(mView)?.showNonNifpManagePolicyView()
    }

    @Test
    fun checkNonActiveNIFPPolicy_Success() {
        val isActivePolicy = false
        val isNIFPPolicy = true
        val isHocClaim = false
        val isPolicyMcl = false
        val isPolicyPl = false
        val isPolicyLegalExpense = false
        mFragmentPresenter!!.checkPolicyAndShowView(
            isActivePolicy, isNIFPPolicy, isHocClaim, isPolicyMcl, isPolicyPl, isPolicyLegalExpense
        )
        Mockito.verify(mView)?.showPolicyLapsedView()
    }

    @Test
    fun checkNonActiveHocPolicy_Success() {
        val isActivePolicy = false
        val isNIFPPolicy = false
        val isHocClaim = true
        val isPolicyMcl = false
        val isPolicyPl = false
        val isPolicyLegalExpense = false
        mFragmentPresenter!!.checkPolicyAndShowView(
            isActivePolicy, isNIFPPolicy, isHocClaim, isPolicyMcl, isPolicyPl, isPolicyLegalExpense
        )
        Mockito.verify(mView)?.showHocLapsedView()
    }

    @Test
    fun checkNonActiveNonNIFPPolicy_Success() {
        val isActivePolicy = false
        val isNIFPPolicy = false
        val isHocClaim = false
        val isPolicyMcl = false
        val isPolicyPl = false
        val isPolicyLegalExpense = false
        mFragmentPresenter!!.checkPolicyAndShowView(
            isActivePolicy, isNIFPPolicy, isHocClaim, isPolicyMcl, isPolicyPl, isPolicyLegalExpense
        )
        Mockito.verify(mView)?.showNonNifpPolicyLapsedView()
    }

    @Test
    fun checkLapsedMclPolicy_Success() {
        val isActivePolicy = false
        val isNIFPPolicy = false
        val isHocClaim = false
        val isPolicyMcl = true
        val isPolicyPl = false
        val isPolicyLegalExpense = false
        mFragmentPresenter!!.checkPolicyAndShowView(
            isActivePolicy, isNIFPPolicy, isHocClaim, isPolicyMcl, isPolicyPl, isPolicyLegalExpense
        )
        Mockito.verify(mView)?.showMclLapsedView()
    }

    @Test
    fun checkActiveMclPolicy_Success() {
        val isActivePolicy = true
        val isNIFPPolicy = false
        val isHocClaim = false
        val isPolicyMcl = true
        val isPolicyPl = false
        val isPolicyLegalExpense = false
        mFragmentPresenter!!.checkPolicyAndShowView(
            isActivePolicy, isNIFPPolicy, isHocClaim, isPolicyMcl, isPolicyPl, isPolicyLegalExpense
        )
        Mockito.verify(mView)?.showMclClaimView()
    }

    @Test
    fun checkLapsedPlPolicy_Success() {
        val isActivePolicy = false
        val isNIFPPolicy = false
        val isHocClaim = false
        val isPolicyMcl = false
        val isPolicyPl = true
        val isPolicyLegalExpense = false
        mFragmentPresenter!!.checkPolicyAndShowView(
            isActivePolicy, isNIFPPolicy, isHocClaim, isPolicyMcl, isPolicyPl, isPolicyLegalExpense
        )
        Mockito.verify(mView)?.showPlLapsedView()
    }

    @Test
    fun checkActivePlPolicy_Success() {
        val isActivePolicy = true
        val isNIFPPolicy = false
        val isHocClaim = false
        val isPolicyMcl = false
        val isPolicyPl = true
        val isPolicyLegalExpense = false

        // Mock feature flag to be enabled (false means feature is enabled)
        Mockito.`when`(mFeatureSetController!!.isFeatureDisabled(FeatureConstants.FTR_INSURANCE_PERSONAL_LINES_POLICY_ADMIN))
            .thenReturn(false)

        mFragmentPresenter!!.checkPolicyAndShowView(
            isActivePolicy, isNIFPPolicy, isHocClaim, isPolicyMcl, isPolicyPl, isPolicyLegalExpense
        )
        Mockito.verify(mView)?.showPLManagePolicyView()
    }

    @Test
    fun checkLegalExpensePolicy_Success() {
        val isNIFPPolicy = false
        val isHocClaim = false
        val isPolicyMcl = false
        val isPolicyPl = false
        val isPolicyLegalExpense = true

        // Test lapsed legal expense policy - should call showPlLapsedView()
        mFragmentPresenter!!.checkPolicyAndShowView(
            false, isNIFPPolicy, isHocClaim, isPolicyMcl, isPolicyPl, isPolicyLegalExpense
        )
        Mockito.verify(mView)?.showPlLapsedView()

        // Test active legal expense policy with feature enabled
        Mockito.`when`(mFeatureSetController!!.isFeatureDisabled(FeatureConstants.FTR_INSURANCE_PERSONAL_LINES_POLICY_ADMIN))
            .thenReturn(false)
        mFragmentPresenter.checkPolicyAndShowView(
            true, isNIFPPolicy, isHocClaim, isPolicyMcl, isPolicyPl, isPolicyLegalExpense
        )
        Mockito.verify(mView)?.showPLManagePolicyView()
    }

    @Test
    fun checkActivePlPolicyFeatureDisabled_Success() {
        val isActivePolicy = true
        val isNIFPPolicy = false
        val isHocClaim = false
        val isPolicyMcl = false
        val isPolicyPl = true
        val isPolicyLegalExpense = false

        // Mock feature flag to be disabled (true means feature is disabled)
        Mockito.`when`(mFeatureSetController!!.isFeatureDisabled(FeatureConstants.FTR_INSURANCE_PERSONAL_LINES_POLICY_ADMIN))
            .thenReturn(true)

        mFragmentPresenter!!.checkPolicyAndShowView(
            isActivePolicy, isNIFPPolicy, isHocClaim, isPolicyMcl, isPolicyPl, isPolicyLegalExpense
        )
        Mockito.verify(mView)?.showPlClaimView()
    }

    @Test
    fun checkActiveLegalExpensePolicyFeatureDisabled_Success() {
        val isNIFPPolicy = false
        val isHocClaim = false
        val isPolicyMcl = false
        val isPolicyPl = false
        val isPolicyLegalExpense = true

        // Mock feature flag to be disabled (true means feature is disabled)
        Mockito.`when`(mFeatureSetController!!.isFeatureDisabled(FeatureConstants.FTR_INSURANCE_PERSONAL_LINES_POLICY_ADMIN))
            .thenReturn(true)

        mFragmentPresenter!!.checkPolicyAndShowView(
            true, isNIFPPolicy, isHocClaim, isPolicyMcl, isPolicyPl, isPolicyLegalExpense
        )
        Mockito.verify(mView)?.showLegalExpensesPolicyView()
    }

    @Test
    fun checkActivePaPolicy_Success() {
        mFragmentPresenter!!.checkPaPolicy(true)
        Mockito.verify(mView)?.showPaClaimView()
    }

    @Test
    fun checkActivePlLapsedPolicy_Success() {
        mFragmentPresenter!!.checkPaPolicy(false)
        Mockito.verify(mView)?.showPlLapsedView()
        mFragmentPresenter.checkVVAPPolicy(
            VVAPPolicyParams(
            false, false, false, false,
            false, false, false,
            false, false, false, false
        ))
        Mockito.verify(mView)?.showVVAPLapsedView()
    }

    @Test
    fun checkActiveVVAPPolicy_Success() {
        Mockito.`when`(mFeatureSetController!!.isFeatureDisabled(FeatureConstants.FTR_INSURANCE_VVAPS_CSF_POLICY_ADMIN))
            .thenReturn(false)
        mFragmentPresenter!!.checkVVAPPolicy(
            VVAPPolicyParams(
            true, true, false, false,
            false, false, false,
            false, false, false, false
        ))
        Mockito.verify(mView)?.showVVAPCSFView()
        Mockito.`when`(mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_INSURANCE_VVAPS_GAP_POLICY_ADMIN))
            .thenReturn(false)
        mFragmentPresenter.checkVVAPPolicy(
            VVAPPolicyParams(
            true, false, true, false,
            false, false, false,
            false, false, false, false
        ))
        Mockito.verify(mView)?.showVVAPGAPView()
        Mockito.`when`(mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_INSURANCE_VVAPS_TLC_POLICY_ADMIN))
            .thenReturn(false)
        mFragmentPresenter.checkVVAPPolicy(
            VVAPPolicyParams(
            true, false, false, true,
            false, false, false,
            false, false, false, false
        ))
        Mockito.verify(mView)?.showVVAPTLCView()
        Mockito.`when`(mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_INSURANCE_VVAPS_DENT_SCRATCH_POLICY_ADMIN))
            .thenReturn(false)
        mFragmentPresenter.checkVVAPPolicy(
            VVAPPolicyParams(
            true, false, false, false,
            true, false, false,
            false, false, false, false
        ))
        Mockito.verify(mView)?.showVVAPDentScratchView()
        Mockito.`when`(mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_INSURANCE_VVAPS_TYRE_RIM_POLICY_ADMIN))
            .thenReturn(false)
        mFragmentPresenter.checkVVAPPolicy(
            VVAPPolicyParams(
            true, false, false, false,
            false, true, false,
            false, false, false, false
        ))
        Mockito.verify(mView)?.showVVAPTyreRimView()
        Mockito.`when`(mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_INSURANCE_VVAPS_ESSENTIAL_POLICY_ADMIN))
            .thenReturn(false)
        mFragmentPresenter.checkVVAPPolicy(
            VVAPPolicyParams(
            true, false, false, false,
            false, false, true,
            false, false, false, false
        ))
        Mockito.verify(mView)?.showVVAPEssentialView()
        Mockito.`when`(mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_INSURANCE_VVAPS_COMPREHENSIVE_POLICY_ADMIN))
            .thenReturn(false)
        mFragmentPresenter.checkVVAPPolicy(
            VVAPPolicyParams(
            true, false, false, false,
            false, false, false,
            true, false, false, false
        ))
        Mockito.verify(mView)?.showVVAPEssentialView()
        mFragmentPresenter.checkVVAPPolicy(
            VVAPPolicyParams(
            true, false, false, false,
            false, false, false,
            false, false, false, false
        ))
        Mockito.verify(mView)?.showVVAPPolicyView()
    }

    @Test
    fun emailApplicationNotFound() {
        mFragmentPresenter!!.emailApplicationNotFound()
        Mockito.verify(mView)?.showNoEmailAppError()
    }

    @Test
    fun navigateEditFinanceDetailsTest() {
        val accountViewModel = AccountViewModel()
        val navigationTarget =
            NavigationTarget.to(ServicesNavigationTarget.INSURANCE_VVAPS_FINANCE_DETAILS)
                .withParam(InsuranceConstants.ParamKeys.PARAM_POLICY_DETAILS, accountViewModel)
        mFragmentPresenter!!.editFinanceDetails(accountViewModel)
        Mockito.verify(mNavigationRouter)?.navigateTo(navigationTarget)
    }

    @Test
    fun navigateEditInsurerDetailsTest() {
        val accountViewModel = AccountViewModel()
        val navigationTarget =
            NavigationTarget.to(ServicesNavigationTarget.INSURANCE_VVAPS_INSURER_DETAILS)
                .withParam(InsuranceConstants.ParamKeys.PARAM_POLICY_DETAILS, accountViewModel)
        mFragmentPresenter!!.editInsurerDetails(accountViewModel)
        Mockito.verify(mNavigationRouter)?.navigateTo(navigationTarget)
    }

    @Test
    fun navigateEditPremiumDetailsTest() {
        val accountViewModel = AccountViewModel()
        val navigationTarget =
            NavigationTarget.to(ServicesNavigationTarget.INSURANCE_VVAPS_PREMIUM_DETAILS)
                .withParam(InsuranceConstants.ParamKeys.PARAM_POLICY_DETAILS, accountViewModel)
        mFragmentPresenter!!.editPremiumDetails(
            accountViewModel, PolicyFlowFlags(StringUtils.EMPTY_STRING,
            true, false, false, false, false, false,false), SUBPRODUCT_CSF
        )
        Mockito.verify(mNavigationRouter)?.navigateTo(navigationTarget)
    }

    @Test
    fun navigateEditVehicleDetailsTest() {
        val accountViewModel = AccountViewModel()
        val navigationTarget =
            NavigationTarget.to(ServicesNavigationTarget.INSURANCE_VVAPS_VEHICLE_DETAILS)
                .withParam(InsuranceConstants.ParamKeys.PARAM_POLICY_DETAILS, accountViewModel)
        mFragmentPresenter!!.editVehicleDetails(accountViewModel, true)
        Mockito.verify(mNavigationRouter)?.navigateTo(navigationTarget)
    }

    @Test
    fun navigateEditBankingDetailsTest() {
        val accountViewModel = AccountViewModel()
        val navigationTarget =
            NavigationTarget.to(ServicesNavigationTarget.INSURANCE_VVAPS_BANKING_DETAILS)
                .withParam(InsuranceConstants.ParamKeys.PARAM_POLICY_DETAILS, accountViewModel)
        mFragmentPresenter!!.editBankingDetails(
            accountViewModel,PolicyFlowFlags(StringUtils.EMPTY_STRING, false, false, false, false, false, false, true),SUBPRODUCT_CSF
        )
        Mockito.verify(mNavigationRouter)?.navigateTo(navigationTarget)
    }

    @Test
    fun navigateEditPaymentDetailsTest() {
        val accountViewModel = AccountViewModel()
        val navigationTarget =
            NavigationTarget.to(ServicesNavigationTarget.INSURANCE_VVAPS_PAYMENT_DAY_DETAILS)
                .withParam(InsuranceConstants.ParamKeys.PARAM_POLICY_DETAILS, accountViewModel)
        mFragmentPresenter!!.editPaymentDetails(
            accountViewModel,PolicyFlowFlags( StringUtils.EMPTY_STRING,true, true, false, false, false, false), SUBPRODUCT_CSF
        )
        Mockito.verify(mNavigationRouter)?.navigateTo(navigationTarget)
    }


    @Test
    fun editWarrantyOptionDetailsTest() {
        val accountViewModel = AccountViewModel()
        val navigationTarget =
            NavigationTarget.to(ServicesNavigationTarget.INSURANCE_VVAPS_WARRANTY_OPTIONS_DETAILS)
                .withParam(InsuranceConstants.ParamKeys.PARAM_POLICY_DETAILS, accountViewModel)
                .withParam(InsuranceConstants.ParamKeys.PARAM_IS_VVAP_GAP_FLOW, true)
        mFragmentPresenter!!.editWarrantyOptionDetails(accountViewModel, true)
        Mockito.verify(mNavigationRouter)?.navigateTo(navigationTarget)
    }

    @Test
    fun editWarrantyDetailsTest() {
        val accountViewModel = AccountViewModel()
        val navigationTarget =
            NavigationTarget.to(ServicesNavigationTarget.INSURANCE_VVAPS_WARRANTY_DETAILS)
                .withParam(InsuranceConstants.ParamKeys.PARAM_POLICY_DETAILS, accountViewModel)
                .withParam(InsuranceConstants.ParamKeys.PARAM_IS_VVAP_GAP_FLOW, true)
        mFragmentPresenter!!.editWarrantyDetails(accountViewModel, true)
        Mockito.verify(mNavigationRouter)?.navigateTo(navigationTarget)
    }

    @Test
    fun sendEventWithProduct() {
        val cdata = HashMap<String?, Any?>()
        val adobeContextData = AdobeContextData(cdata)
        adobeContextData.setCategoryAndProduct(
            TrackingEvent.ANALYTICS.INSURANCE_PRODUCT_CATEGORY, TrackingEvent.ANALYTICS.VVAPS_TITLE
        )
        adobeContextData.setProductCategory(TrackingEvent.ANALYTICS.INSURENCE)
        adobeContextData.setProductAccount(TrackingEvent.ANALYTICS.VVAPS_TITLE)
        adobeContextData.setSubProduct("")
        mFragmentPresenter!!.sendEventWithProduct(
            InsuranceTrackingEvent.EVENT_MANAGE_POLICY_BANKING_JOURNEY, ""
        )
        Mockito.verify(mAnalytics)?.sendEventActionWithMap(
            InsuranceTrackingEvent.EVENT_MANAGE_POLICY_BANKING_JOURNEY, cdata
        )
    }

    @Test
    fun showAPIErrorTest() {
        mFragmentPresenter!!.showAPIError()
        Mockito.verify(mView)?.showProgressBar(false)
        Mockito.verify(mView)?.showAPIError()
    }

    @Test
    fun showViewProgressBarTest() {
        mFragmentPresenter!!.showViewProgressBar(true)
        Mockito.verify(mView)?.showProgressBar(true)

        mFragmentPresenter.showViewProgressBar(false)
        Mockito.verify(mView)?.showProgressBar(false)
    }

    @Test
    fun showAPIErrorNullViewTest() {
        mFragmentPresenter!!.unbind()
        mFragmentPresenter.showAPIError()
        Mockito.verifyNoMoreInteractions(mView)
    }

    @Test
    fun showViewProgressBarNullViewTest() {
        mFragmentPresenter!!.unbind()
        mFragmentPresenter.showViewProgressBar(true)
        Mockito.verifyNoMoreInteractions(mView)
    }

    @Test
    fun editPaymentDetails_withNullParams_shouldNavigate() {
        mFragmentPresenter!!.editPaymentDetails(null, null, null)
        Mockito.verify(mNavigationRouter)?.navigateTo(Mockito.any())
    }

    @Test
    fun editPaymentDetails_withOnlyAccountViewModel_shouldNavigate() {
        val accountViewModel = AccountViewModel()
        mFragmentPresenter!!.editPaymentDetails(accountViewModel)
        Mockito.verify(mNavigationRouter)?.navigateTo(Mockito.any())
    }

    @Test
    fun editPaymentDetails_withAllParams_shouldNavigate() {
        val accountViewModel = AccountViewModel()
        val policyFlowFlags = PolicyFlowFlags(
            StringUtils.EMPTY_STRING,
            isGapFlow = true,
            isTlcFlow = true,
            isDentScratchFlow = true,
            isTyreRimFlow = true,
            isEssentialFlow = true,
            isComprehensiveFlow = true,
            isPolicyNIFP = true,
            isPolicyPL = true
        )
        val subProduct = "TestSubProduct"
        mFragmentPresenter!!.editPaymentDetails(accountViewModel, policyFlowFlags, subProduct)
        Mockito.verify(mNavigationRouter)?.navigateTo(Mockito.any())
    }

    // Note: Frequency logic tests have been moved to InsuranceDetailsContainerPresenterTest
    // as the frequency checking is now handled at the container level for conditional navigation
}