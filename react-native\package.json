{"name": "nedbank-insurance-rn", "version": "0.1.0", "private": true, "scripts": {"start": "react-native start", "android": "react-native run-android", "ios": "react-native run-ios", "test": "jest", "lint": "eslint ."}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "react-native-vector-icons": "^10.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.0", "@react-native/metro-config": "^0.72.0", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "jest": {"preset": "react-native"}}