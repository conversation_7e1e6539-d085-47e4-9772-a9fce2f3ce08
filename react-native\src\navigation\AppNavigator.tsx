import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar } from 'react-native';

// Import screens
import InsuranceDashboard from '../screens/dashboard/InsuranceDashboard';
import FuneralNavigator from './FuneralNavigator';
import PolicyListContainer from '../screens/policies/PolicyListContainer';
import EmergencyServices from '../screens/dashboard/EmergencyServices';
import Education from '../screens/common/Education';
import Error from '../screens/common/Error';
import ThankYou from '../screens/common/ThankYou';

// Navigation types
export type RootStackParamList = {
  InsuranceDashboard: undefined;
  FuneralFlow: undefined;
  PolicyList: undefined;
  EmergencyServices: undefined;
  Education: { productType: string; title: string };
  Error: { message: string; canRetry?: boolean };
  ThankYou: { title: string; message: string; nextAction?: string };
};

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  return (
    <NavigationContainer>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      <Stack.Navigator
        initialRouteName="InsuranceDashboard"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#ffffff',
            elevation: 2,
            shadowOpacity: 0.1,
            shadowRadius: 3,
            shadowOffset: { width: 0, height: 2 },
          },
          headerTintColor: '#333333',
          headerTitleStyle: {
            fontWeight: '500',
            fontSize: 18,
            color: '#333333',
          },
          headerBackTitleVisible: false,
          cardStyle: { backgroundColor: '#ffffff' },
          animationEnabled: true,
          gestureEnabled: true,
        }}
      >
        {/* Main Dashboard */}
        <Stack.Screen
          name="InsuranceDashboard"
          component={InsuranceDashboard}
          options={{
            title: 'Insurance',
            headerShown: true,
          }}
        />

        {/* Funeral Insurance Flow */}
        <Stack.Screen
          name="FuneralFlow"
          component={FuneralNavigator}
          options={{
            headerShown: false, // Funeral navigator handles its own headers
          }}
        />

        {/* Policy Management */}
        <Stack.Screen
          name="PolicyList"
          component={PolicyListContainer}
          options={{
            title: 'Policies and claims',
            headerShown: true,
          }}
        />

        {/* Emergency Services */}
        <Stack.Screen
          name="EmergencyServices"
          component={EmergencyServices}
          options={{
            title: 'Emergency services',
            headerShown: true,
          }}
        />

        {/* Education Screens */}
        <Stack.Screen
          name="Education"
          component={Education}
          options={({ route }) => ({
            title: route.params?.title || 'Learn more',
            headerShown: true,
          })}
        />

        {/* Error Screen */}
        <Stack.Screen
          name="Error"
          component={Error}
          options={{
            title: 'Something went wrong',
            headerShown: true,
          }}
        />

        {/* Thank You / Success Screen */}
        <Stack.Screen
          name="ThankYou"
          component={ThankYou}
          options={({ route }) => ({
            title: route.params?.title || 'Thank you',
            headerShown: true,
            gestureEnabled: false, // Prevent swipe back on success screens
          })}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
