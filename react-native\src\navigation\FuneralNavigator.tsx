import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

// Import Funeral screens
import FuneralPlanSetup from '../screens/funeral/PlanSetup';
import FuneralBeneficiary from '../screens/funeral/Beneficiary';
import FuneralDebitOrder from '../screens/funeral/DebitOrder';
import FuneralEmail from '../screens/funeral/Email';
import FuneralReviewQuote from '../screens/funeral/ReviewQuote';
import FuneralSuccess from '../screens/funeral/Success';

// Navigation types for Funeral flow
export type FuneralStackParamList = {
  PlanSetup: undefined;
  Beneficiary: { planDetails: any };
  DebitOrder: { planDetails: any; beneficiaryDetails: any };
  Email: { planDetails: any; beneficiaryDetails: any; debitDetails: any };
  ReviewQuote: {
    planDetails: any;
    beneficiaryDetails: any;
    debitDetails: any;
    emailDetails: any;
  };
  Success: { quoteReference: string; premium: number };
};

const Stack = createStackNavigator<FuneralStackParamList>();

const FuneralNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName="PlanSetup"
      screenOptions={{
        headerStyle: {
          backgroundColor: '#ffffff',
          elevation: 2,
          shadowOpacity: 0.1,
          shadowRadius: 3,
          shadowOffset: { width: 0, height: 2 },
        },
        headerTintColor: '#333333',
        headerTitleStyle: {
          fontWeight: '500',
          fontSize: 18,
          color: '#333333',
        },
        headerBackTitleVisible: false,
        cardStyle: { backgroundColor: '#ffffff' },
        animationEnabled: true,
        gestureEnabled: true,
      }}
    >
      {/* Step 1: Plan Setup */}
      <Stack.Screen
        name="PlanSetup"
        component={FuneralPlanSetup}
        options={{
          title: 'Funeral cover',
          headerShown: true,
        }}
      />

      {/* Step 2: Beneficiary Details */}
      <Stack.Screen
        name="Beneficiary"
        component={FuneralBeneficiary}
        options={{
          title: 'Beneficiary details',
          headerShown: true,
        }}
      />

      {/* Step 3: Debit Order Setup */}
      <Stack.Screen
        name="DebitOrder"
        component={FuneralDebitOrder}
        options={{
          title: 'Payment details',
          headerShown: true,
        }}
      />

      {/* Step 4: Email Details */}
      <Stack.Screen
        name="Email"
        component={FuneralEmail}
        options={{
          title: 'Contact details',
          headerShown: true,
        }}
      />

      {/* Step 5: Review Quote */}
      <Stack.Screen
        name="ReviewQuote"
        component={FuneralReviewQuote}
        options={{
          title: 'Review your quote',
          headerShown: true,
        }}
      />

      {/* Step 6: Success */}
      <Stack.Screen
        name="Success"
        component={FuneralSuccess}
        options={{
          title: 'Application submitted',
          headerShown: true,
          gestureEnabled: false, // Prevent swipe back on success
          headerLeft: () => null, // Remove back button on success
        }}
      />
    </Stack.Navigator>
  );
};

export default FuneralNavigator;
